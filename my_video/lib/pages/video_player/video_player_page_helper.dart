import 'package:my_video/app_imports.dart';

class VideoPlayerPageHelper {
  final VideoPlayerPageState _state;
  final MovieModel movie;
  final Logger _logger = Logger();

  YoutubePlayerController? _youtubeController;
  YoutubePlayerController? get youtubeController => _youtubeController;

  bool _isFullScreen = false;
  bool get isFullScreen => _isFullScreen;

  bool _isDescriptionExpanded = false;
  bool get isDescriptionExpanded => _isDescriptionExpanded;

  bool _isInPlaylist = false;
  bool get isInPlaylist => _isInPlaylist;

  List<MovieModel> _relatedMovies = [];
  List<MovieModel> get relatedMovies => _relatedMovies;

  ApiStatus apiStatus = ApiStatus.initial;

  VideoPlayerPageHelper(this._state, this.movie) {
    Future.delayed(const Duration(milliseconds: 10), () => _initializeData());
  }

  Future<void> _initializeData() async {
    apiStatus = ApiStatus.loading;
    _state.videoPlayerController.update();

    // Show ad for guest users when video player opens
    if (!AuthHelper.isLoggedIn) {
      AdsManager.showInterstitialAd(
        onAdClosed: () {
          _logger.i('Guest user ad closed, continuing with video initialization');
        },
      );
    }

    _initializePlayer();
    _checkPlaylistStatus();
    await _loadRelatedMovies();

    apiStatus = ApiStatus.success;
    _state.videoPlayerController.update();
  }

  void _initializePlayer() {
    try {
      switch (movie.videoSourceType) {
        case VideoSourceType.youtube:
          _initializeYouTubePlayer();
          break;
        case VideoSourceType.terabox:
          _initializeTeraboxPlayer();
          break;
        case VideoSourceType.other:
          _initializeOtherPlayer();
          break;
      }
    } catch (e) {
      _logger.e('Error initializing video player: $e');
      _showError('Failed to load video');
    }
  }

  void _initializeYouTubePlayer() {
    final videoId = movie.youtubeVideoId;
    if (videoId != null) {
      _youtubeController = YoutubePlayerController.fromVideoId(
        videoId: videoId,
        params: const YoutubePlayerParams(
          showControls: true,
          showFullscreenButton: false, // We'll handle fullscreen ourselves
          mute: false,
          enableCaption: true,
          captionLanguage: 'en',
        ),
      );
      _logger.i('YouTube player initialized for video: $videoId');
      _state.videoPlayerController.update();
    } else {
      _logger.e('Invalid YouTube URL: ${movie.youtubeUrl}');
      _showError('Invalid YouTube video ID');
    }
  }

  void _initializeTeraboxPlayer() {
    _logger.w('Terabox URL detected: ${movie.youtubeUrl}');
    _showError(
      'Terabox videos are not supported yet. Please use YouTube links.',
    );
  }

  void _initializeOtherPlayer() {
    _logger.w('Unknown video source: ${movie.youtubeUrl}');
    _showError('This video format is not supported. Please use YouTube links.');
  }

  void _checkPlaylistStatus() {
    _isInPlaylist = HiveHelper.isMovieInDefaultPlaylist(movie.id);
    _state.videoPlayerController.update();
  }

  Future<void> _loadRelatedMovies() async {
    try {
      final related = await _state.videoPlayerController
          .getRelatedMoviesFromAPI(movie.id, movie.category ?? 'Unknown');
      _relatedMovies = related.take(10).toList();
      _state.videoPlayerController.update();
      _logger.i('Loaded ${_relatedMovies.length} related movies');
    } catch (e) {
      _logger.e('Error loading related movies: $e');
      // Load from cache as fallback
      _relatedMovies = HiveHelper.getMoviesByCategory(
        movie.category ?? 'Unknown',
      ).where((m) => m.id != movie.id).take(10).toList();
      _state.videoPlayerController.update();
    }
  }

  void toggleFullScreen() {
    _isFullScreen = !_isFullScreen;
    _state.videoPlayerController.update();

    if (_isFullScreen) {
      // Enter fullscreen mode
      _enterFullScreen();
    } else {
      // Exit fullscreen mode
      _exitFullScreen();
    }
  }

  void _enterFullScreen() {
    // Hide system UI for immersive fullscreen experience
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // Allow all orientations in fullscreen mode for better user experience
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    _logger.i('Entered fullscreen mode');
  }

  void _exitFullScreen() {
    // Restore system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // Allow portrait orientations when not in fullscreen
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    _logger.i('Exited fullscreen mode');
  }

  void toggleDescription() {
    _isDescriptionExpanded = !_isDescriptionExpanded;
    _state.videoPlayerController.update();
  }

  Future<void> togglePlaylist() async {
    // Check if user is logged in
    if (!AuthHelper.isLoggedIn) {
      AuthHelper.showLoginRequiredBottomSheet(
        _state.context,
        title: 'Login Required',
        message: 'You need to login to add movies to your playlist.',
      );
      return;
    }

    try {
      if (_isInPlaylist) {
        await HiveHelper.removeMovieFromDefaultPlaylist(movie.id);
        _isInPlaylist = false;
        _showSuccess('Removed from playlist');
      } else {
        await HiveHelper.addMovieToDefaultPlaylist(movie.id);
        _isInPlaylist = true;
        _showSuccess('Added to playlist');
      }
      _state.videoPlayerController.update();
    } catch (e) {
      _logger.e('Error toggling playlist: $e');
      _showError('Failed to update playlist');
    }
  }

  void shareMovie() {
    try {
      final shareText =
          '''
Check out this amazing movie: ${movie.title}

${movie.description ?? ''}

Watch it on MyVideo app!
${movie.youtubeUrl}
''';

      Share.share(shareText);
      _logger.i('Shared movie: ${movie.title}');
    } catch (e) {
      _logger.e('Error sharing movie: $e');
      _showError('Failed to share movie');
    }
  }

  void playRelatedMovie(MovieModel relatedMovie, BuildContext context) {
    try {
      _logger.i('Playing related movie: ${relatedMovie.title}');

      // Show interstitial ad before playing related video
      AdsManager.showInterstitialAd(
        onAdClosed: () {
          // Navigate to new video player with related movie using GoRouter
          context.pushReplacement(AppRoutes.moviePlayer, extra: relatedMovie);
        },
      );
    } catch (e) {
      _logger.e('Error playing related movie: $e');
      _showError('Failed to play video');
    }
  }

  void _showSuccess(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorGreen,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 2),
    );
  }

  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorRed,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  void dispose() {
    // Restore system UI when closing
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Dispose YouTube controller
    _youtubeController?.close();
  }
}
