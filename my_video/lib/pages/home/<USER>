import 'package:my_video/app_imports.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => HomePageState();
}

class HomePageState extends State<HomePage> with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  HomePageHelper? _homePageHelper;
  late HomeController homeController;
  late PageController _pageController;
  late TabController _tabController;
  int _currentPage = 0;
  Timer? _autoScrollTimer;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 0.85);
    _tabController = TabController(length: 2, vsync: this);
    // Auto-scroll will be started after helper is initialized
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _autoScrollTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      final helper = _homePageHelper;
      if (helper?.featuredMovies.isNotEmpty == true &&
          _pageController.hasClients &&
          mounted) {
        final nextPage =
            (_currentPage + 1) % helper!.featuredMovies.length;
        _pageController.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
  }

  void _restartAutoScroll() {
    _stopAutoScroll();
    _startAutoScroll();
  }

  void _showFilterBottomSheet() {
    showPreferenceSelectionBottomSheet(
      context,
      title: 'Filter Movies',
      onFiltersSelected: (selectedFilters) {
        // Refresh the page with new filters
        _homePageHelper?.refreshWithFilters(selectedFilters);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Use GetBuilder to make authentication state reactive
    return GetBuilder<MainNavigationController>(
      builder: (navController) {
        // Check authentication status and show appropriate view
        if (!AuthHelper.isLoggedIn) {
          // User is not logged in - show guest home page
          return const GuestHomePage();
        }

        // User is logged in - show category-wise home page
        return GetBuilder<HomeController>(
          init: Get.put(HomeController()),
          builder: (HomeController controller) {
            homeController = controller;
            if (_homePageHelper == null) {
              _homePageHelper = HomePageHelper(this);
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  _startAutoScroll();
                }
              });
            }
            return Scaffold(extendBody: true, body: _bodyView());
          },
        );
      },
    );
  }

  Widget _appBar() {
    return Padding(
      padding: const EdgeInsets.all(8.0).copyWith(left: 16),
      child: Row(
        children: [
          const AppText(
            text: 'My Video',
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(
              Icons.search,
              color: AppColorConstants.textPrimary,
            ),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const HomeSearchPage(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(
              Icons.filter_list,
              color: AppColorConstants.textPrimary,
            ),
            onPressed: () => _showFilterBottomSheet(),
          ),
        ],
      ),
    );
  }

  Widget _bodyView() {
    final helper = _homePageHelper;
    if (helper == null) {
      return SafeArea(bottom: false, child: ShimmerWidgets.buildHomePageShimmer());
    }

    return helper.isLoading
        ? SafeArea(bottom: false, child: ShimmerWidgets.buildHomePageShimmer())
        : SafeArea(
            bottom: false,
            child: RefreshIndicator(
              onRefresh: helper.refreshData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _appBar(),
                    _buildTabBar(),
                    if (helper.featuredMovies.isNotEmpty) ...[
                      Space.height(30),
                      _buildFeaturedBanner(),
                      Space.height(24),
                    ],

                    ...helper.categories.map((category) {
                      final movies = helper.getMoviesByCategory(
                        category.name,
                      );
                      if (movies.isEmpty) return const SizedBox.shrink();

                      return _buildCategorySection(category, movies);
                    }),

                    Space.height(
                      kBottomNavigationBarHeight + 80,
                    ), // Bottom padding for FAB
                  ],
                ),
              ),
            ),
          );
  }

  Widget _buildTabBar() {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
        decoration: BoxDecoration(
          color: AppColorConstants.cardColor,
          borderRadius: BorderRadius.circular(MySize.width(8)),
        ),
        child: TabBar(
          controller: _tabController,
          onTap: (index) {
            final contentType = MovieContentType.values[index];
            final isMovie = contentType.apiValue;
            _homePageHelper?.loadDataForContentType(isMovie);
          },
          indicator: BoxDecoration(
            color: AppColorConstants.primaryColor,
            borderRadius: BorderRadius.circular(MySize.width(8)),
          ),
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: Colors.transparent,
          labelColor: AppColorConstants.colorWhite,
          unselectedLabelColor: AppColorConstants.colorGrey,
          labelStyle: TextStyle(
            fontSize: MySize.width(14),
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: MySize.width(14),
            fontWeight: FontWeight.normal,
          ),
          tabs: MovieContentType.values
              .map((contentType) => Tab(text: contentType.displayName))
              .toList(),
        ),
    );
  }

  Widget _buildFeaturedBanner() {
    final helper = _homePageHelper;
    if (helper == null || helper.featuredMovies.isEmpty) {
      return SizedBox(
        height: MySize.height(250),
        child: LoadingManager.buildLoadingWidget(
          message: 'Loading featured movies...',
        ),
      );
    }

    return Column(
      children: [
        GestureDetector(
          onPanDown: (_) => _stopAutoScroll(),
          onPanEnd: (_) => _restartAutoScroll(),
          child: SizedBox(
            height: MySize.height(220),
            child: PageView.builder(
              itemCount: helper.featuredMovies.length,
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemBuilder: (context, index) {
                final movie = helper.featuredMovies[index];
                return BannerMovieCard(
                  movie: movie,
                  onTap: () => helper.playMovie(movie, context),
                );
              },
            ),
          ),
        ),
        Space.height(12),
        _buildDotIndicators(),
      ],
    );
  }

  Widget _buildDotIndicators() {
    final helper = _homePageHelper;
    if (helper == null) return const SizedBox.shrink();

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        helper.featuredMovies.length,
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: MySize.width(4)),
          width: MySize.width(_currentPage == index ? 24 : 8),
          height: MySize.height(8),
          decoration: BoxDecoration(
            color: _currentPage == index
                ? AppColorConstants.primaryColor
                : AppColorConstants.dividerColor,
            borderRadius: BorderRadius.circular(MySize.radius(4)),
          ),
        ),
      ),
    );
  }

  Widget _buildCategorySection(
    CategoryWiseData category,
    List<MovieModel> movies,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                text: category.name,
                fontSize: MySize.fontSize(18),
                fontWeight: FontWeight.bold,
                color: AppColorConstants.textPrimary,
              ),
              GestureDetector(
                onTap: () {
                  final helper = _homePageHelper;
                  if (helper != null) {
                    helper.viewAllMovies(category);
                  }
                },
                child: AppText(
                  text: 'See All',
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Space.height(12),
        SizedBox(
          height: MySize.height(240),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            itemCount: movies.length + 1, // +1 for single ad
            itemBuilder: (context, index) {
              final helper = _homePageHelper;
              if (helper == null) return const SizedBox.shrink();

              final categoryIndex = helper.categories.indexOf(category);
              final isEvenCategory = categoryIndex % 2 == 0;
              final adPosition = isEvenCategory ? 0 : movies.length;

              if (index == adPosition) {
                final bannerAd = AdsManager.getBannerAdWidget(
                  index: categoryIndex % 10,
                  uniqueId: 'category_${category.name}_ad_$categoryIndex',
                );
                return bannerAd ?? AdsManager.buildAdPlaceholder();
              }

              // Movie items
              final movieIndex = isEvenCategory ? index - 1 : index;
              if (movieIndex < 0 || movieIndex >= movies.length) {
                return const SizedBox.shrink();
              }

              final movie = movies[movieIndex];
              return MovieCard(
                movie: movie,
                onTap: () => helper.playMovie(movie, context),
                showTitle: true,
                showRating: true,
                showDuration: true,
              );
            },
          ),
        ),
        Space.height(24),
      ],
    );
  }
}
