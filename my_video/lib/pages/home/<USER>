import 'package:my_video/app_imports.dart';

class HomePageHelper {
  final HomePageState _state;
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = true;
  bool get isLoading => _isLoading;

  // Data variables
  List<MovieModel> _featuredMovies = [];
  List<CategoryWiseData> _categoryWiseData = [];
  Map<String, List<MovieModel>> _moviesByCategory = {};

  // Content type variable - "0" for movies, "1" for web series
  String _currentContentType = "1"; // Default to movies
  String get currentContentType => _currentContentType;

  // Method to change content type
  void setContentType(String contentType) {
    _currentContentType = contentType;
  }

  // Getters
  List<MovieModel> get featuredMovies => _featuredMovies;
  List<CategoryWiseData> get categories => _categoryWiseData;

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;
  bool _hasLoadedData = false;

  HomePageHelper(this._state) {
    if (!_hasLoadedData) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (!_hasLoadedData) {
          loadInitialData();
        }
      });
    }
  }

  Future<void> loadInitialData() async {
    _setLoading(true);
    apiStatus = ApiStatus.loading;

    try {
      // Check if offline and has cached data
      final offlineManager = OfflineManager.instance;
      if (!offlineManager.isOnline && offlineManager.hasCachedData()) {
        await _loadCachedData();
        _setLoading(false);
        return;
      }

      // Ensure user is still logged in before making authenticated API calls
      if (!AuthHelper.isLoggedIn) {
        _logger.w(
          'User is no longer logged in, cannot load authenticated data',
        );
        apiStatus = ApiStatus.error;
        return;
      }

      // Ensure authentication token is available first, then load data
      _logger.i('Ensuring auth token before loading data...');
      await _ensureAuthToken();
      await loadCategoryWiseData();

      apiStatus = ApiStatus.success;
      _hasLoadedData = true;
      _logger.i('Successfully loaded data from showcatwise API');
    } catch (e) {
      _logger.e('Error loading initial data: $e');
      ErrorHandler.handleError(e, context: 'Home Data Loading');
      apiStatus = ApiStatus.error;

      // Try to load cached data as fallback
      await _loadCachedData();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _ensureAuthToken() async {
    try {
      // For authenticated users, we should already have a user token
      final userToken = AppSharedPreference.getUserToken();
      if (userToken != null && userToken.isNotEmpty) {
        _logger.i('User authentication token available');
        return;
      }

      // If no user token but we're in authenticated mode, something is wrong
      if (AuthHelper.isLoggedIn) {
        _logger.w('User appears logged in but no token found');
        return;
      }

      // Fallback: Get static token (shouldn't happen in authenticated mode)
      final staticToken = AppSharedPreference.getString('static_token');
      if (staticToken != null && staticToken.isNotEmpty) {
        _logger.i('Static token already available');
        return;
      }

      // Get unregistered user token as last resort
      _logger.i('Getting unregistered user token...');

      final response = await RestHelper.post('/unregisteredusertoken').timeout(
        const Duration(seconds: 10),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 1) {
          await AppSharedPreference.setString('static_token', data['token']);
          _logger.i('Unregistered token obtained successfully');
        } else {
          _logger.w('Failed to get unregistered token: ${data['message']}');
        }
      } else {
        _logger.w('Failed to get unregistered token: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error getting authentication token: $e');
    }
  }

  Future<void> loadCategoryWiseData() async {
    try {
      _logger.i(
        'Loading category wise data for ${_currentContentType == "1" ? 'web series' : 'movies'}',
      );

      // Check internet connectivity before making API call
      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        _logger.w('No internet connection available');
        throw Exception('No internet connection');
      }

      // Get user preferences for filtering
      final languageFilter = await UserPreferenceService.instance
          .getLanguageNamesForAPI();
      final genreFilter = await UserPreferenceService.instance
          .getGenreNamesForAPI();
      final categoryFilter = await UserPreferenceService.instance
          .getCategoryNamesForAPI();

      final response = await _state.homeController
          .getCategoryWiseMoviesFromAPI(
            languages: languageFilter,
            genres: genreFilter,
            categories: categoryFilter,
            isMovie: _currentContentType,
          );

      if (response.success && response.result.isNotEmpty) {
        _categoryWiseData = response.result;

        _featuredMovies = [];
        for (final categoryData in _categoryWiseData) {
          if (_featuredMovies.length < 5) {
            _featuredMovies.addAll(
              categoryData.data.take(5 - _featuredMovies.length),
            );
          }
        }

        _moviesByCategory.clear();
        for (final categoryData in _categoryWiseData) {
          _moviesByCategory[categoryData.name] = categoryData.data;
        }

        _logger.i(
          'Loaded category wise data: ${_categoryWiseData.length} categories, ${_featuredMovies.length} featured movies',
        );
        // Defer the update to the next frame to avoid build scope issues
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_state.mounted) {
            _state.homeController.update();
          }
        });
      } else {
        _logger.w('No data received from showcatwise API');
      }
    } catch (e) {
      _logger.e('Error loading category wise data: $e');
      rethrow;
    }
  }

  Future<void> _loadCachedData() async {
    try {
      final offlineManager = OfflineManager.instance;
      _featuredMovies = offlineManager.getCachedFeaturedMovies();

      // For now, create empty category wise data from cached data
      _categoryWiseData = [];
      _moviesByCategory.clear();

      _logger.i(
        'Loaded cached data: ${_featuredMovies.length} featured movies',
      );
      apiStatus = ApiStatus.success;
      _hasLoadedData = true;
    } catch (e) {
      _logger.e('Error loading cached data: $e');
      apiStatus = ApiStatus.error;
    }
  }

  List<MovieModel> getMoviesByCategory(String categoryName) {
    return _moviesByCategory[categoryName] ?? [];
  }

  Future<void> refreshData() async {
    try {
      _logger.i('Refreshing home page data with current tab state');

      // Keep current content type (no need to get from tab controller)
      _logger.i('Current content type: $_currentContentType');

      // Clear existing data
      _categoryWiseData.clear();
      _featuredMovies.clear();
      _moviesByCategory.clear();

      // Set loading state
      _setLoading(true);
      apiStatus = ApiStatus.loading;

      // Ensure user is still logged in before making authenticated API calls
      if (!AuthHelper.isLoggedIn) {
        _logger.w(
          'User is no longer logged in, cannot refresh authenticated data',
        );
        apiStatus = ApiStatus.error;
        return;
      }

      // Ensure authentication token is available first, then load data
      await _ensureAuthToken();
      await loadCategoryWiseData();

      apiStatus = ApiStatus.success;
      _hasLoadedData = true;
      _logger.i('Successfully refreshed home page data');
    } catch (e) {
      _logger.e('Error refreshing home page data: $e');
      ErrorHandler.handleError(e, context: 'Home Data Refresh');
      apiStatus = ApiStatus.error;

      // Try to load cached data as fallback
      await _loadCachedData();
    } finally {
      _setLoading(false);
    }
  }

  // Method to handle authentication state changes
  Future<void> onAuthenticationStateChanged() async {
    try {
      _logger.i('Authentication state changed, refreshing home page data');
      _hasLoadedData = false;
      await loadInitialData();
    } catch (e) {
      _logger.e('Error handling authentication state change: $e');
    }
  }

  void playMovie(MovieModel movie, BuildContext context) {
    _logger.i('Playing movie: ${movie.title}');

    AdsManager.showInterstitialAd(
      onAdClosed: () {
        context.push(AppRoutes.moviePlayer, extra: movie);
      },
    );
  }

  void viewAllMovies(CategoryWiseData category) {
    _logger.i('View all movies for category: ${category.name}');
    AppHelper.showToast('View all ${category.name} movies');
  }

  // Load data for specific content type (Movies or Web Series)
  Future<void> loadDataForContentType(String contentType) async {
    try {
      AppHelper.logDebug('Loading data for content type: $contentType');

      // Update the instance variable
      _currentContentType = contentType;

      // Clear existing data
      _categoryWiseData.clear();
      _featuredMovies.clear();
      _moviesByCategory.clear();

      // Set loading state
      _setLoading(true);

      // Ensure user is still logged in before making authenticated API calls
      if (!AuthHelper.isLoggedIn) {
        AppHelper.logDebug(
          'User is no longer logged in, cannot load content type data',
        );
        return;
      }

      // Ensure authentication token is available first, then load data with specific content type
      await _ensureAuthToken();
      await loadCategoryWiseData();

      AppHelper.logDebug('Successfully loaded data for content type: $contentType');
    } catch (e) {
      AppHelper.logDebug('Error loading data for content type: $e');
      ErrorHandler.handleError(e);
    } finally {
      _setLoading(false);
    }
  }

  // Refresh data with new filters
  Future<void> refreshWithFilters(SelectedFilters filters) async {
    try {
      AppHelper.logDebug(
        'Refreshing logged-in home page with filters: $filters',
      );

      // Save filters to preferences
      await UserPreferenceService.instance.saveSelectedFilters(filters);

      // Clear existing data
      _categoryWiseData.clear();
      _featuredMovies.clear();
      _moviesByCategory.clear();

      // Ensure user is still logged in before making authenticated API calls
      if (!AuthHelper.isLoggedIn) {
        AppHelper.logDebug(
          'User is no longer logged in, cannot refresh with filters',
        );
        return;
      }

      // Reload data with new filters and current content type
      await _ensureAuthToken();
      await loadCategoryWiseData();

      AppHelper.logDebug(
        'Successfully refreshed logged-in home page with filters',
      );
    } catch (e) {
      AppHelper.logDebug('Error refreshing with filters: $e');
      ErrorHandler.handleError(e);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    // Defer the update to the next frame to avoid build scope issues
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_state.mounted) {
        _state.homeController.update();
      }
    });
  }
}
