import 'package:my_video/app_imports.dart';




class GuestHomePageHelper {
  final HomeController _homeController;
  final Logger _logger = Logger();

  // Loading states
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;

  // Data variables
  List<MovieModel> _sliderMovies = [];
  List<MovieModel> _gridMovies = [];
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Content type variable - "0" for movies, "1" for web series
  String _currentContentType = "1"; // Default to movies

  // Getters
  List<MovieModel> get sliderMovies => _sliderMovies;
  List<MovieModel> get gridMovies => _gridMovies;
  bool get hasSliderMovies => _sliderMovies.isNotEmpty;

  // Method to change content type
  void setContentType(String contentType) {
    _currentContentType = contentType;
  }
  bool get hasMoreData => _hasMoreData;

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;
  bool _hasLoadedInitialData = false;

  GuestHomePageHelper(this._homeController) {
    // Load initial data immediately for better UX
    if (!_hasLoadedInitialData) {
      loadInitialData();
    }
  }

  Future<void> loadInitialData() async {
    _setLoading(true);
    apiStatus = ApiStatus.loading;
    _homeController.update();

    try {
      // Check if offline and has cached data
      final offlineManager = OfflineManager.instance;
      if (!offlineManager.isOnline && offlineManager.hasCachedData()) {
        await _loadCachedData();
        _setLoading(false);
        _homeController.update();
        return;
      }

      // Run token and data loading in parallel for better performance
      _logger.i('Loading guest data and ensuring auth token in parallel...');

      // Use current content type (default to movies)
      final isMovie = _currentContentType;

      await Future.wait([
        _ensureAuthToken(),
        _loadShowPosts(page: 1, isMovie: isMovie),
      ]);

      apiStatus = ApiStatus.success;
      _hasLoadedInitialData = true;
      _logger.i('Successfully loaded data from showposts API');
    } catch (e) {
      _logger.e('Error loading initial guest data: $e');
      ErrorHandler.handleError(e, context: 'Guest Home Data Loading');
      apiStatus = ApiStatus.error;

      // Try to load cached data as fallback
      await _loadCachedData();
    } finally {
      _setLoading(false);
      _homeController.update();
    }
  }

  Future<void> _ensureAuthToken() async {
    try {
      // Check if we already have a static token
      final existingToken = AppSharedPreference.getString('static_token');
      if (existingToken != null && existingToken.isNotEmpty) {
        _logger.i('Static token already exists');
        return;
      }

      // Get unregistered user token
      _logger.i('Getting unregistered user token...');
      final response = await RestHelper.post('/unregisteredusertoken');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 1) {
          await AppSharedPreference.setString('static_token', data['token']);
          _logger.i('Unregistered token obtained successfully');
        } else {
          _logger.w('Failed to get unregistered token: ${data['message']}');
        }
      } else {
        _logger.w('Failed to get unregistered token: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error getting authentication token: $e');
    }
  }

  Future<void> _loadShowPosts({
    required int page,
    bool isLoadMore = false,
    String? isMovie,
  }) async {
    try {
      // Get user preferences for filtering
      final languageFilter = await UserPreferenceService.instance
          .getLanguageNamesForAPI();
      final genreFilter = await UserPreferenceService.instance
          .getGenreNamesForAPI();
      final categoryFilter = await UserPreferenceService.instance
          .getCategoryNamesForAPI();

      final response = await _homeController.getShowPostsFromAPI(
        page: page,
        languages: languageFilter,
        genres: genreFilter,
        categories: categoryFilter,
        isMovie: isMovie,
      );

      if (response.success) {
        if (page == 1) {
          // Initial load - set slider and grid data
          _sliderMovies = response.sliderPost ?? [];
          _gridMovies = response.result;
          _currentPage = 1;
        } else {
          // Load more - append to grid data
          _gridMovies.addAll(response.result);
        }

        // Update pagination state
        _hasMoreData =
            response.result.length >= 15; // API returns 15 items per page
        if (response.result.isNotEmpty) {
          _currentPage = page;
        }

        _logger.i(
          'Loaded show posts: page $page, ${response.result.length} movies, '
          'slider: ${_sliderMovies.length}, grid: ${_gridMovies.length}',
        );
        _homeController.update();
      } else {
        _logger.w('Show posts API returned unsuccessful response');
        if (page == 1) {
          _hasMoreData = false;
        }
      }
    } catch (e) {
      _logger.e('Error loading show posts: $e');
      if (page == 1) {
        rethrow;
      } else {
        // For load more errors, just log and continue
        _hasMoreData = false;
      }
    }
  }

  Future<void> loadMoreMovies() async {
    if (_isLoadingMore || !_hasMoreData) return;

    _setLoadingMore(true);

    try {
      // Use current content type
      final isMovie = _currentContentType;

      await _loadShowPosts(
        page: _currentPage + 1,
        isLoadMore: true,
        isMovie: isMovie,
      );
    } catch (e) {
      _logger.e('Error loading more movies: $e');
      AppHelper.showToast('Failed to load more movies', isError: true);
    } finally {
      _setLoadingMore(false);
    }
  }

  // Load data for specific content type (Movies or Web Series)
  Future<void> loadDataForContentType(String isMovie) async {
    try {
      AppHelper.logDebug('Loading data for content type: $isMovie');

      // Update the content type
      setContentType(isMovie);

      // Reset pagination and data
      _currentPage = 1;
      _hasMoreData = true;
      _sliderMovies.clear();
      _gridMovies.clear();

      // Set loading state
      _setLoading(true);

      // Load data with specific content type
      await _loadShowPosts(page: 1, isMovie: isMovie);

      AppHelper.logDebug('Successfully loaded data for content type: $isMovie');
    } catch (e) {
      AppHelper.logDebug('Error loading data for content type: $e');
      ErrorHandler.handleError(e);
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadCachedData() async {
    try {
      final offlineManager = OfflineManager.instance;
      final cachedMovies = offlineManager.getCachedFeaturedMovies();

      // Use cached movies for grid view
      _gridMovies = cachedMovies;
      _sliderMovies = []; // No slider for cached data

      _logger.i('Loaded cached data: ${_gridMovies.length} movies');
      apiStatus = ApiStatus.success;
      _hasLoadedInitialData = true;
    } catch (e) {
      _logger.e('Error loading cached data: $e');
      apiStatus = ApiStatus.error;
    }
  }

  Future<void> refreshData() async {
    // Reset pagination state
    _currentPage = 1;
    _hasMoreData = true;
    _hasLoadedInitialData = false;

    await loadInitialData();
  }

  void playMovie(MovieModel movie, BuildContext context) {
    _logger.i('Playing movie: ${movie.title}');

    AdsManager.showInterstitialAd(
      onAdClosed: () {
        context.push(AppRoutes.moviePlayer, extra: movie);
      },
    );
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _homeController.update();
  }

  void _setLoadingMore(bool loading) {
    _isLoadingMore = loading;
    _homeController.update();
  }

  // Refresh data with new filters
  Future<void> refreshWithFilters(SelectedFilters filters) async {
    try {
      AppHelper.logDebug('Refreshing guest home page with filters: $filters');

      // Save filters to preferences
      await UserPreferenceService.instance.saveSelectedFilters(filters);

      // Reset pagination
      _currentPage = 1;
      _hasMoreData = true;
      _sliderMovies.clear();
      _gridMovies.clear();

      // Reload data with new filters and current content type
      await _loadShowPosts(page: 1, isMovie: _currentContentType);

      AppHelper.logDebug('Successfully refreshed guest home page with filters');
    } catch (e) {
      AppHelper.logDebug('Error refreshing with filters: $e');
      ErrorHandler.handleError(e);
    }
  }
}
