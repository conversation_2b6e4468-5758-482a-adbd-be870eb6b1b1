import 'package:my_video/app_imports.dart';

class PlaylistPageHelper {
  final PlaylistPageState _state;
  final Logger _logger = Logger();

  // UI State variables
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  List<MovieModel> _playlistMovies = [];
  List<MovieModel> get playlistMovies => _playlistMovies;

  PlaylistModel? _defaultPlaylist;
  PlaylistModel? get defaultPlaylist => _defaultPlaylist;

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;

  PlaylistPageHelper(this._state) {
    Future.delayed(
      const Duration(milliseconds: 10),
      () => loadPlaylistMovies(),
    );
  }

  Future<void> loadPlaylistMovies() async {
    _setLoading(true);
    apiStatus = ApiStatus.loading;
    _state.playlistController.update();

    try {
      // Get default playlist
      _defaultPlaylist = HiveHelper.getDefaultPlaylist();

      if (_defaultPlaylist != null) {
        // Get movies from playlist
        _playlistMovies = HiveHelper.getPlaylistMovies(_defaultPlaylist!.id);
        _logger.i('Loaded ${_playlistMovies.length} movies from playlist');
      } else {
        _playlistMovies = [];
        _logger.w('No default playlist found');
      }

      apiStatus = ApiStatus.success;
    } catch (e) {
      _logger.e('Error loading playlist movies: $e');
      _showError('Failed to load playlist');
      apiStatus = ApiStatus.error;
    } finally {
      _setLoading(false);
      _state.playlistController.update();
    }
  }

  Future<void> refreshPlaylist() async {
    await loadPlaylistMovies();
  }

  void playMovie(MovieModel movie, BuildContext context) {
    _logger.i('Playing movie from playlist: ${movie.title}');

    AdsManager.showInterstitialAd(
      onAdClosed: () {
        context.push(AppRoutes.moviePlayer, extra: movie);
      },
    );
  }

  Future<void> removeFromPlaylist(MovieModel movie) async {
    try {
      await HiveHelper.removeMovieFromDefaultPlaylist(movie.id);
      await loadPlaylistMovies(); // Refresh the list
      _showSuccess('Removed from playlist');
      _logger.i('Removed movie from playlist: ${movie.title}');
    } catch (e) {
      _logger.e('Error removing movie from playlist: $e');
      _showError('Failed to remove from playlist');
    }
  }

  Future<void> clearPlaylist() async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const AppText(
            text: 'Clear Playlist',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          content: const AppText(
            text:
                'Are you sure you want to remove all movies from your playlist?',
            fontSize: 14,
            color: AppColorConstants.textSecondary,
          ),
          backgroundColor: AppColorConstants.surfaceColor,
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const AppText(
                text: 'Cancel',
                color: AppColorConstants.textSecondary,
              ),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const AppText(
                text: 'Clear',
                color: AppColorConstants.colorRed,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Clear all movies from playlist
        for (final movie in _playlistMovies) {
          await HiveHelper.removeMovieFromDefaultPlaylist(movie.id);
        }
        await loadPlaylistMovies(); // Refresh the list
        _showSuccess('Playlist cleared');
        _logger.i('Playlist cleared');
      }
    } catch (e) {
      _logger.e('Error clearing playlist: $e');
      _showError('Failed to clear playlist');
    }
  }

  void sharePlaylist() {
    try {
      if (_playlistMovies.isEmpty) {
        _showError('Playlist is empty');
        return;
      }

      final movieTitles = _playlistMovies
          .map((movie) => movie.title)
          .join('\n• ');
      final shareText =
          '''
My Movie Playlist from MyVideo:

• $movieTitles

Download MyVideo app to watch these amazing movies!
''';

      Share.share(shareText);
      _logger.i('Shared playlist with ${_playlistMovies.length} movies');
    } catch (e) {
      _logger.e('Error sharing playlist: $e');
      _showError('Failed to share playlist');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _state.playlistController.update();
  }

  void _showSuccess(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorGreen,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 2),
    );
  }

  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorRed,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }
}
