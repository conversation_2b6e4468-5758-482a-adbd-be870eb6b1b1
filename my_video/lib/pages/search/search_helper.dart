import 'package:my_video/app_imports.dart';

class SearchHelper {
  final HomeController _homeController;
  final Logger _logger = Logger();

  // Loading states
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isSearching = false;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get isSearching => _isSearching;

  // Search data
  String _currentSearchQuery = '';
  String get currentSearchQuery => _currentSearchQuery;

  // Content type variables - "0" for movies, "1" for web series
  final String _currentMovieContentType = "1"; // Default to movies
  final String _currentSeriesContentType = "0"; // Default to web series

  // Data for movies and web series
  List<MovieModel> _movieResults = [];
  List<MovieModel> _seriesResults = [];
  List<MovieModel> _currentResults = [];

  // Pagination
  int _moviePage = 1;
  int _seriesPage = 1;
  bool _movieHasMoreData = true;
  bool _seriesHasMoreData = true;

  // Current tab (0 = movies, 1 = web series)
  int _currentTabIndex = 0;
  int get currentTabIndex => _currentTabIndex;

  // Getters
  List<MovieModel> get movieResults => _movieResults;
  List<MovieModel> get seriesResults => _seriesResults;
  List<MovieModel> get currentResults => _currentResults;
  bool get hasResults => _currentResults.isNotEmpty;
  bool get hasMoreData => _currentTabIndex == 0 ? _movieHasMoreData : _seriesHasMoreData;

  // Search state
  bool _hasSearched = false;
  bool get hasSearched => _hasSearched;

  SearchHelper(this._homeController);

  // Set current tab and update results
  void setCurrentTab(int tabIndex) {
    _currentTabIndex = tabIndex;
    _updateCurrentResults();
    _homeController.update();
  }

  void _updateCurrentResults() {
    _currentResults = _currentTabIndex == 0 ? _movieResults : _seriesResults;
  }

  // Perform search for both movies and web series in parallel
  Future<void> searchContent(String query) async {
    if (query.trim().isEmpty) {
      clearSearch();
      return;
    }

    _currentSearchQuery = query.trim();
    _setSearching(true);
    _hasSearched = true;

    try {
      _logger.i('Searching for: "$query"');

      // Reset pagination and data
      _resetSearchData();

      // Get user preferences for filtering
      final languageFilter = await UserPreferenceService.instance
          .getLanguageNamesForAPI();
      final genreFilter = await UserPreferenceService.instance
          .getGenreNamesForAPI();
      final categoryFilter = await UserPreferenceService.instance
          .getCategoryNamesForAPI();

      // Search both movies and web series in parallel
      final futures = [
        _searchMovies(
          query,
          page: 1,
          languages: languageFilter,
          genres: genreFilter,
          categories: categoryFilter,
        ),
        _searchSeries(
          query,
          page: 1,
          languages: languageFilter,
          genres: genreFilter,
          categories: categoryFilter,
        ),
      ];

      await Future.wait(futures);

      _updateCurrentResults();
      _logger.i(
        'Search completed: ${_movieResults.length} movies, ${_seriesResults.length} series',
      );
    } catch (e) {
      _logger.e('Error searching content: $e');
      ErrorHandler.handleError(e, context: 'Search Content');
    } finally {
      _setSearching(false);
    }
  }

  // Search movies
  Future<void> _searchMovies(
    String query, {
    required int page,
    String? languages,
    String? genres,
    String? categories,
  }) async {
    try {
      final response = await _homeController.searchPostsFromAPI(
        query,
        page: page,
        isMovie: _currentMovieContentType,
        languages: languages,
        genres: genres,
        categories: categories,
      );

      if (response.success) {
        if (page == 1) {
          _movieResults = response.result;
          _moviePage = 1;
        } else {
          _movieResults.addAll(response.result);
        }

        _movieHasMoreData = response.result.length >= 10; // API returns 10 items per page
        if (response.result.isNotEmpty) {
          _moviePage = page;
        }

        _logger.i('Loaded movie search results: page $page, ${response.result.length} movies');
      } else {
        _logger.w('Movie search API returned unsuccessful response');
        if (page == 1) {
          _movieHasMoreData = false;
        }
      }
    } catch (e) {
      _logger.e('Error searching movies: $e');
      if (page == 1) {
        _movieHasMoreData = false;
      }
      rethrow;
    }
  }

  // Search web series
  Future<void> _searchSeries(
    String query, {
    required int page,
    String? languages,
    String? genres,
    String? categories,
  }) async {
    try {
      final response = await _homeController.searchPostsFromAPI(
        query,
        page: page,
        isMovie: _currentSeriesContentType,
        languages: languages,
        genres: genres,
        categories: categories,
      );

      if (response.success) {
        if (page == 1) {
          _seriesResults = response.result;
          _seriesPage = 1;
        } else {
          _seriesResults.addAll(response.result);
        }

        _seriesHasMoreData = response.result.length >= 10; // API returns 10 items per page
        if (response.result.isNotEmpty) {
          _seriesPage = page;
        }

        _logger.i('Loaded series search results: page $page, ${response.result.length} series');
      } else {
        _logger.w('Series search API returned unsuccessful response');
        if (page == 1) {
          _seriesHasMoreData = false;
        }
      }
    } catch (e) {
      _logger.e('Error searching series: $e');
      if (page == 1) {
        _seriesHasMoreData = false;
      }
      rethrow;
    }
  }

  // Load more results for current tab
  Future<void> loadMoreResults() async {
    if (!hasMoreData || _isLoadingMore || _currentSearchQuery.isEmpty) return;

    _setLoadingMore(true);

    try {
      final languageFilter = await UserPreferenceService.instance
          .getLanguageNamesForAPI();
      final genreFilter = await UserPreferenceService.instance
          .getGenreNamesForAPI();
      final categoryFilter = await UserPreferenceService.instance
          .getCategoryNamesForAPI();

      if (_currentTabIndex == 0) {
        // Load more movies
        await _searchMovies(
          _currentSearchQuery,
          page: _moviePage + 1,
          languages: languageFilter,
          genres: genreFilter,
          categories: categoryFilter,
        );
      } else {
        // Load more series
        await _searchSeries(
          _currentSearchQuery,
          page: _seriesPage + 1,
          languages: languageFilter,
          genres: genreFilter,
          categories: categoryFilter,
        );
      }

      _updateCurrentResults();
    } catch (e) {
      _logger.e('Error loading more results: $e');
      ErrorHandler.handleError(e, context: 'Load More Search Results');
    } finally {
      _setLoadingMore(false);
    }
  }

  // Clear search
  void clearSearch() {
    _currentSearchQuery = '';
    _hasSearched = false;
    _resetSearchData();
    _updateCurrentResults();
    _homeController.update();
  }

  // Reset search data
  void _resetSearchData() {
    _movieResults.clear();
    _seriesResults.clear();
    _moviePage = 1;
    _seriesPage = 1;
    _movieHasMoreData = true;
    _seriesHasMoreData = true;
  }

  // Refresh search with filters
  Future<void> refreshWithFilters(SelectedFilters filters) async {
    try {
      _logger.i('Refreshing search with filters: $filters');

      // Save filters to preferences
      await UserPreferenceService.instance.saveSelectedFilters(filters);

      // Re-search if we have a query
      if (_currentSearchQuery.isNotEmpty) {
        await searchContent(_currentSearchQuery);
      }

      _logger.i('Successfully refreshed search with filters');
    } catch (e) {
      _logger.e('Error refreshing search with filters: $e');
      ErrorHandler.handleError(e);
    }
  }

  // Play movie
  void playMovie(MovieModel movie, BuildContext context) {
    _logger.i('Playing movie: ${movie.title}');

    AdsManager.showInterstitialAd(
      onAdClosed: () {
        context.push(AppRoutes.moviePlayer, extra: movie);
      },
    );
  }

  // Helper methods
  void _setSearching(bool searching) {
    _isSearching = searching;
    _homeController.update();
  }

  void _setLoadingMore(bool loading) {
    _isLoadingMore = loading;
    _homeController.update();
  }
}
