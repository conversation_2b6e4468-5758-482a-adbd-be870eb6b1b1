import 'package:my_video/app_imports.dart';

class HomeSearchPage extends StatefulWidget {
  const HomeSearchPage({super.key});

  @override
  State<HomeSearchPage> createState() => _HomeSearchPageState();
}

class _HomeSearchPageState extends State<HomeSearchPage> with TickerProviderStateMixin {
  late HomeController _homeController;
  late TabController _tabController;
  SearchHelper? _searchHelper;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _setupScrollListener();
    
    // Auto-focus search field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      // Load more when user scrolls to 80% of the content
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        _searchHelper?.loadMoreResults();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorConstants.backgroundColor,
      body: GetBuilder<HomeController>(
        init: Get.put(HomeController(), permanent: true),
        builder: (HomeController controller) {
          _homeController = controller;
          _searchHelper ??= SearchHelper(_homeController);
          return _bodyView();
        },
      ),
    );
  }

  Widget _bodyView() {
    final helper = _searchHelper;
    if (helper == null) {
      return SafeArea(
        bottom: false,
        child: ShimmerWidgets.buildGuestHomePageShimmer(),
      );
    }

    return SafeArea(
      bottom: false,
      child: Column(
        children: [
          // Search Bar
          _buildSearchBar(),

          // Tab Bar
          _buildTabBar(),

          // Content
          Expanded(
            child: helper.isSearching
                ? _buildSearchingIndicator()
                : !helper.hasSearched
                    ? _buildInitialState()
                    : !helper.hasResults
                        ? _buildEmptySearchState()
                        : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(MySize.width(16)),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.arrow_back,
              color: AppColorConstants.textPrimary,
              size: MySize.width(24),
            ),
          ),

          Space.width(8),

          // Search field
          Expanded(
            child: AppTextFormField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              hintText: 'Search movies and web series...',
              prefixIcon: Icon(
                Icons.search,
                color: AppColorConstants.textSecondary,
                size: MySize.width(20),
              ),
              suffixIcon: _searchController.text.isNotEmpty
                  ? GestureDetector(
                      onTap: () {
                        _searchController.clear();
                        _searchHelper?.clearSearch();
                      },
                      child: Icon(
                        Icons.clear,
                        color: AppColorConstants.textSecondary,
                        size: MySize.width(20),
                      ),
                    )
                  : null,
              onChanged: (value) {
                setState(() {}); // Update UI for clear button
              },
              onTap: () {
                // Auto-focus when tapped
              },
              textInputAction: TextInputAction.search,
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _searchHelper?.searchContent(value.trim());
                }
              },
            ),
          ),

          Space.width(8),

          // Search button
          IconButton(
            onPressed: () {
              if (_searchController.text.trim().isNotEmpty) {
                _searchHelper?.searchContent(_searchController.text.trim());
              }
            },
            icon: Icon(
              Icons.search,
              color: AppColorConstants.primaryColor,
              size: MySize.width(24),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.width(8)),
      ),
      child: TabBar(
        controller: _tabController,
        onTap: (index) {
          _searchHelper?.setCurrentTab(index);
        },
        indicator: BoxDecoration(
          color: AppColorConstants.primaryColor,
          borderRadius: BorderRadius.circular(MySize.width(8)),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: AppColorConstants.colorWhite,
        unselectedLabelColor: AppColorConstants.colorGrey,
        labelStyle: TextStyle(
          fontSize: MySize.width(14),
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: MySize.width(14),
          fontWeight: FontWeight.normal,
        ),
        tabs: MovieContentType.values
            .map((contentType) => Tab(text: contentType.displayName))
            .toList(),
      ),
    );
  }

  Widget _buildSearchingIndicator() {
    return Column(
      children: [
        Space.height(60),
        // Show ads while searching
        _buildSearchAd(),
        Space.height(40),
        CircularProgressIndicator(color: AppColorConstants.primaryColor),
        Space.height(16),
        AppText(
          text: 'Searching...',
          fontSize: 16,
          color: AppColorConstants.textSecondary,
        ),
      ],
    );
  }

  Widget _buildInitialState() {
    return Column(
      children: [
        Space.height(60),
        // Show ads when user first opens search
        _buildSearchAd(),
        Space.height(40),
        Icon(
          Icons.search,
          size: MySize.height(64),
          color: AppColorConstants.textHint,
        ),
        Space.height(16),
        AppText(
          text: 'Search for movies and web series',
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: AppColorConstants.textSecondary,
        ),
        Space.height(8),
        AppText(
          text: 'Enter a title, genre, or keyword to get started',
          fontSize: 14,
          color: AppColorConstants.textHint,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmptySearchState() {
    return Column(
      children: [
        Space.height(60),
        Icon(
          Icons.search_off,
          size: MySize.height(64),
          color: AppColorConstants.textHint,
        ),
        Space.height(16),
        AppText(
          text: 'No results found',
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: AppColorConstants.textSecondary,
        ),
        Space.height(8),
        AppText(
          text: 'Try searching with different keywords',
          fontSize: 14,
          color: AppColorConstants.textHint,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSearchAd() {
    final bannerAd = AdsManager.getBannerAdWidget(
      index: 0,
      uniqueId: 'search_page_ad',
    );
    
    if (bannerAd == null) return const SizedBox.shrink();
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        border: Border.all(
          color: AppColorConstants.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        child: bannerAd,
      ),
    );
  }

  Widget _buildSearchResults() {
    final helper = _searchHelper;
    if (helper == null || helper.currentResults.isEmpty) {
      return _buildEmptySearchState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        if (helper.currentSearchQuery.isNotEmpty) {
          await helper.searchContent(helper.currentSearchQuery);
        }
      },
      child: CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          // Grid View
          SliverToBoxAdapter(child: _buildGridSection()),

          // Load More Indicator
          if (helper.isLoadingMore)
            SliverToBoxAdapter(child: _buildLoadMoreIndicator()),

          // Bottom padding
          SliverToBoxAdapter(child: Space.height(120)),
        ],
      ),
    );
  }

  Widget _buildGridSection() {
    final helper = _searchHelper;
    if (helper == null || helper.currentResults.isEmpty) {
      return _buildEmptySearchState();
    }

    // Calculate total items including ads (add ad every 6 items)
    final adInterval = 6;
    final totalAds = (helper.currentResults.length / adInterval).floor();
    final totalItems = helper.currentResults.length + totalAds;

    return GridView.builder(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.width(16),
        vertical: MySize.height(30),
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: MySize.width(20),
        mainAxisSpacing: MySize.height(20),
      ),
      itemCount: totalItems,
      itemBuilder: (context, index) {
        // Check if this position should be an ad
        final adPositions = <int>[];
        for (int i = 1; i <= totalAds; i++) {
          adPositions.add((i * adInterval) + (i - 1));
        }

        if (adPositions.contains(index)) {
          // Show ad
          final adIndex = adPositions.indexOf(index);
          final bannerAd = AdsManager.getBannerAdWidget(
            index: adIndex % 10,
            uniqueId: 'search_grid_ad_$adIndex',
          );
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(MySize.radius(12)),
              border: Border.all(
                color: AppColorConstants.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(MySize.radius(12)),
              child: bannerAd ?? AdsManager.buildAdPlaceholder(),
            ),
          );
        }

        // Calculate movie index (accounting for ads before this position)
        final adsBeforeThisPosition = adPositions.where((pos) => pos < index).length;
        final movieIndex = index - adsBeforeThisPosition;

        if (movieIndex >= helper.currentResults.length) {
          return const SizedBox.shrink();
        }

        final movie = helper.currentResults[movieIndex];
        return MovieCard(
          margin: EdgeInsets.zero,
          movie: movie,
          onTap: () => helper.playMovie(movie, context),
          showTitle: true,
          showRating: true,
          showDuration: true,
        );
      },
    );
  }

  Widget _buildLoadMoreIndicator() {
    return Container(
      padding: EdgeInsets.all(MySize.height(16)),
      child: Center(
        child: CircularProgressIndicator(color: AppColorConstants.primaryColor),
      ),
    );
  }
}
