import 'package:my_video/app_imports.dart';

class MainNavigationPage extends StatelessWidget {
  const MainNavigationPage({super.key});

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    return GetBuilder<MainNavigationController>(
      init: MainNavigationController(),
      builder: (controller) {
        return Scaffold(
          extendBody: true,
          body: Column(
            children: [
              GetBuilder<OfflineManager>(
                builder: (offlineManager) =>
                    offlineManager.buildOfflineIndicator(),
              ),

              Expanded(
                child: IndexedStack(
                  index: controller.currentIndex,
                  children: controller.pages,
                ),
              ),
            ],
          ),
          bottomNavigationBar: _buildBottomNavigationBar(controller),
          floatingActionButton: _buildFloatingActionButton(controller, context),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
        );
      },
    );
  }

  Widget _buildBottomNavigationBar(MainNavigationController controller) {
    return BottomAppBar(
      padding: EdgeInsets.zero,
      height: kBottomNavigationBarHeight + MySize.height(25),
      shape: const CircularNotchedRectangle(),
      notchMargin: 8.0,
      color: AppColorConstants.surfaceColor,
      elevation: 8,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(
            icon: Icons.home_outlined,
            activeIcon: Icons.home,
            label: 'Home',
            index: 0,
            controller: controller,
          ),
          _buildNavItem(
            icon: Icons.playlist_play_outlined,
            activeIcon: Icons.playlist_play,
            label: 'My Playlist',
            index: 1,
            controller: controller,
          ),
          SizedBox(width: MySize.width(40)),
          _buildNavItem(
            icon: Icons.subscriptions_outlined,
            activeIcon: Icons.subscriptions,
            label: 'Subscription',
            index: 2,
            controller: controller,
          ),
          _buildNavItem(
            icon: Icons.settings_outlined,
            activeIcon: Icons.settings,
            label: 'Settings',
            index: 3,
            controller: controller,
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required IconData activeIcon,
    required String label,
    required int index,
    required MainNavigationController controller,
  }) {
    final isActive = controller.currentIndex == index;

    return GestureDetector(
      onTap: () => controller.changeTab(index),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: MySize.width(8)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isActive ? activeIcon : icon,
              color: isActive
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.textSecondary,
              size: MySize.height(24),
            ),
            Space.height(2),
            AppText(
              text: label,
              fontSize: MySize.fontSize(10),
              color: isActive
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.textSecondary,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(
    MainNavigationController controller,
    BuildContext context,
  ) {
    return FloatingActionButton(
      onPressed: () {
        controller.showAddMovieDialog(context);
      },
      backgroundColor: AppColorConstants.primaryColor,
      foregroundColor: AppColorConstants.textPrimary,
      shape: const CircleBorder(),
      elevation: 6,
      child: Icon(Icons.add, size: MySize.height(28)),
    );
  }
}
