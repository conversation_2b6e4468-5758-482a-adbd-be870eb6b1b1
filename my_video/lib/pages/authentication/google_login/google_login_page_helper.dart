import 'package:my_video/app_imports.dart';

class GoogleLoginPageHelper {
  final VoidCallback _updateState;
  final Logger _logger = Logger();
  final AuthenticationController _authController = AuthenticationController();

  // Form controllers
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();

  // State variables
  bool _isLoading = false;
  ApiStatus apiStatus = ApiStatus.initial;

  GoogleLoginPageHelper(this._updateState);

  bool get isLoading => _isLoading;

  void _setLoading(bool loading) {
    _isLoading = loading;
    _updateState();
  }

  // Validation methods
  String? validateFirstName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'First name is required';
    }
    if (value.trim().length < 2) {
      return 'First name must be at least 2 characters';
    }
    return null;
  }

  String? validateLastName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Last name is required';
    }
    if (value.trim().length < 2) {
      return 'Last name must be at least 2 characters';
    }
    return null;
  }

  // Google Sign-In method
  Future<void> signInWithGoogle() async {
    // Validate names first
    if (firstNameController.text.trim().isEmpty) {
      AppHelper.showToast('Please enter your first name', isError: true);
      return;
    }
    if (lastNameController.text.trim().isEmpty) {
      AppHelper.showToast('Please enter your last name', isError: true);
      return;
    }

    try {
      _setLoading(true);
      apiStatus = ApiStatus.loading;

      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        apiStatus = ApiStatus.noInternet;
        AppHelper.showToast('No internet connection', isError: true);
        return;
      }

      // Sign in with Google
      final googleUser = await GoogleAuthService.signInWithGoogle();
      if (googleUser == null) {
        apiStatus = ApiStatus.error;
        AppHelper.showToast('Google Sign-In was cancelled', isError: true);
        return;
      }

      // Use the names from the form instead of Google profile
      final firstName = firstNameController.text.trim();
      final lastName = lastNameController.text.trim();

      // Call backend API with Google user data
      final result = await _authController.googleSignInFromAPI(
        email: googleUser.email,
        firstName: firstName,
        lastName: lastName,
        photoUrl: googleUser.photoUrl,
        googleId: googleUser.id,
      );

      if (result['success'] == true) {
        final token = result['data']['token'] as String;
        final userData = result['data']['user'] as Map<String, dynamic>;

        // Save to local storage
        await AppSharedPreference.setUserToken(token);
        await AppSharedPreference.setUserData(userData);

        apiStatus = ApiStatus.success;
        AppHelper.showToast('Welcome! Sign-in successful');

        // Force refresh of authentication state in all controllers
        await _refreshAuthenticationState();

        // Add a small delay to ensure all state changes are propagated
        await Future.delayed(const Duration(milliseconds: 200));

        // Check if user needs to set preferences
        await _checkAndNavigateToPreferences();
      } else {
        apiStatus = ApiStatus.error;
        AppHelper.showToast(
          result['message'] ?? 'Sign-in failed',
          isError: true,
        );
      }
    } catch (e) {
      apiStatus = ApiStatus.error;
      _logger.e('Google Sign-In error: $e');
      AppHelper.showToast('Something went wrong', isError: true);
    } finally {
      _setLoading(false);
    }
  }

  // Continue as guest
  Future<void> continueAsGuest() async {
    try {
      // Check if user needs to set preferences first
      await _checkAndNavigateToPreferences();
    } catch (e) {
      _logger.e('Continue as guest error: $e');
      AppHelper.showToast('Something went wrong', isError: true);
    }
  }

  // Refresh authentication state across all controllers
  Future<void> _refreshAuthenticationState() async {
    try {
      // Update all GetX controllers that depend on authentication state
      if (Get.isRegistered<HomeController>()) {
        Get.find<HomeController>().update();
      }
      if (Get.isRegistered<MainNavigationController>()) {
        Get.find<MainNavigationController>().update();
      }
      if (Get.isRegistered<SettingsController>()) {
        Get.find<SettingsController>().update();
      }

      _logger.i('Authentication state refreshed across all controllers');

      // Add a small delay to ensure UI updates are complete
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      _logger.e('Error refreshing authentication state: $e');
    }
  }

  // Check preferences and navigate accordingly
  Future<void> _checkAndNavigateToPreferences() async {
    try {
      // Check if user has completed preferences setup
      final preferencesCompleted = await UserPreferenceService.instance
          .hasSetPreferences();

      if (!preferencesCompleted) {
        // Navigate to initial setup for preference selection
        router.go(AppRoutes.initialSetup);
      } else {
        // Navigate to main navigation
        router.go(AppRoutes.mainNavigation);
      }
    } catch (e) {
      _logger.e('Error checking preferences: $e');
      // Default to main navigation if there's an error
      router.go(AppRoutes.mainNavigation);
    }
  }

  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
  }
}
