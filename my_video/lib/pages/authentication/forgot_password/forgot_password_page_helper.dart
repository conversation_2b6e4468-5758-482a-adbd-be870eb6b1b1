import 'package:my_video/app_imports.dart';

class ForgotPasswordPageHelper {
  final ForgotPasswordPageState _state;
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Form controller
  final TextEditingController forgotPasswordEmailController =
      TextEditingController();

  // Form key
  final GlobalKey<FormState> forgotPasswordFormKey = GlobalKey<FormState>();

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;

  ForgotPasswordPageHelper(this._state) {
    _initializeData();
  }

  void _initializeData() {
    _logger.i('Forgot password page initialized');
  }

  Future<void> forgotPassword() async {
    if (!forgotPasswordFormKey.currentState!.validate()) {
      return;
    }

    try {
      _setLoading(true);
      apiStatus = ApiStatus.loading;
      _state.authController.update();

      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        apiStatus = ApiStatus.noInternet;
        AppHelper.showToast('no_internet_connection'.tr, isError: true);
        return;
      }

      final result = await _state.authController.forgotPasswordFromAPI(
        email: forgotPasswordEmailController.text.trim(),
      );

      if (result['success'] == true) {
        apiStatus = ApiStatus.success;
        AppHelper.showToast('Password reset link sent to your email');

        // Clear form
        forgotPasswordEmailController.clear();

        // Navigate back to login
        gotoLoginPage();
      } else {
        apiStatus = ApiStatus.error;
        AppHelper.showToast(
          result['message'] ?? 'failed_to_send_reset_link',
          isError: true,
        );
      }
    } catch (e) {
      apiStatus = ApiStatus.error;
      AppHelper.logError('Forgot password error', e);
      AppHelper.showToast('something_went_wrong', isError: true);
    } finally {
      _setLoading(false);
      _state.authController.update();
    }
  }

  void navigateToLogin() {
    try {
      gotoGoogleLoginPage();
    } catch (e) {
      _logger.e('Error navigating to login: $e');
    }
  }

  void navigateToContactSupport() {
    try {
      gotoContactSupportPage();
    } catch (e) {
      _logger.e('Error navigating to contact support: $e');
    }
  }

  // Validation Methods
  String? validateEmail(String? value) {
    return ValidationHelper.validateEmail(value);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _state.authController.update();
  }

  void dispose() {
    forgotPasswordEmailController.dispose();
    _logger.i('Forgot password page helper disposed');
  }
}
