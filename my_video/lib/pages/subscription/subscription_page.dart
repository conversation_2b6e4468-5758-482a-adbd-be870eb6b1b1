import 'package:my_video/app_imports.dart';

class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({super.key});

  @override
  State<SubscriptionPage> createState() => SubscriptionPageState();
}

class SubscriptionPageState extends State<SubscriptionPage> {
  SubscriptionPageHelper? _subscriptionPageHelper;
  late SubscriptionController subscriptionController;

  @override
  Widget build(BuildContext context) {
    _subscriptionPageHelper =
        _subscriptionPageHelper ?? SubscriptionPageHelper(this);

    return GetBuilder(
      init: SubscriptionController(),
      builder: (SubscriptionController controller) {
        subscriptionController = controller;
        return Scaffold(
          appBar: AppBar(
            title: const AppText(
              text: 'Subscription',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
            actions: [
              TextButton(
                onPressed: _subscriptionPageHelper!.restorePurchases,
                child: const AppText(
                  text: 'Restore',
                  color: AppColorConstants.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          body: _subscriptionPageHelper!.isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: EdgeInsets.all(MySize.width(16)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Current status card
                      _buildCurrentStatusCard(),
                      Space.height(24),

                      const AppText(
                        text: 'Choose Your Plan',
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColorConstants.textPrimary,
                      ),
                      Space.height(16),

                      ..._subscriptionPageHelper!.subscriptionPlans.map((plan) {
                        return Column(
                          children: [_buildPlanCard(plan), Space.height(16)],
                        );
                      }),
                      Space.height(kBottomNavigationBarHeight + 80),
                    ],
                  ),
                ),
        );
      },
    );
  }

  Widget _buildCurrentStatusCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.width(16)),
      decoration: BoxDecoration(
        color: _subscriptionPageHelper!.isPremiumUser
            ? AppColorConstants.primaryColor.withValues(alpha: 0.1)
            : AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        border: Border.all(
          color: _subscriptionPageHelper!.isPremiumUser
              ? AppColorConstants.primaryColor
              : AppColorConstants.dividerColor,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _subscriptionPageHelper!.isPremiumUser
                    ? Icons.star
                    : Icons.person_outline,
                color: _subscriptionPageHelper!.isPremiumUser
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.textSecondary,
                size: MySize.height(24),
              ),
              Space.width(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      text: _subscriptionPageHelper!.isPremiumUser
                          ? 'Premium User'
                          : 'Free User',
                      fontSize: MySize.fontSize(18),
                      fontWeight: FontWeight.bold,
                      color: AppColorConstants.textPrimary,
                    ),
                    AppText(
                      text: _subscriptionPageHelper!.isPremiumUser
                          ? 'Enjoying ad-free experience'
                          : 'Upgrade to remove ads',
                      fontSize: MySize.fontSize(14),
                      color: AppColorConstants.textSecondary,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCard(SubscriptionPlan plan) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: plan.isCurrentPlan
            ? AppColorConstants.primaryColor.withValues(alpha: 0.1)
            : AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        border: Border.all(
          color: plan.isPopular
              ? AppColorConstants.primaryColor
              : plan.isCurrentPlan
              ? AppColorConstants.primaryColor
              : AppColorConstants.dividerColor,
          width: plan.isPopular ? 2 : 1,
        ),
      ),
      child: Stack(
        children: [
          if (plan.isPopular)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.width(12),
                  vertical: MySize.height(4),
                ),
                decoration: BoxDecoration(
                  color: AppColorConstants.primaryColor,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(MySize.radius(12)),
                    bottomLeft: Radius.circular(MySize.radius(8)),
                  ),
                ),
                child: AppText(
                  text: 'POPULAR',
                  fontSize: MySize.fontSize(10),
                  fontWeight: FontWeight.bold,
                  color: AppColorConstants.textPrimary,
                ),
              ),
            ),

          Padding(
            padding: EdgeInsets.all(MySize.width(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppText(
                            text: plan.name,
                            fontSize: MySize.fontSize(18),
                            fontWeight: FontWeight.bold,
                            color: AppColorConstants.textPrimary,
                          ),
                          Space.height(4),
                          AppText(
                            text: plan.description,
                            fontSize: MySize.fontSize(14),
                            color: AppColorConstants.textSecondary,
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (plan.price > 0) ...[
                          AppText(
                            text: '\$${plan.price.toStringAsFixed(2)}',
                            fontSize: MySize.fontSize(24),
                            fontWeight: FontWeight.bold,
                            color: AppColorConstants.primaryColor,
                          ),
                          AppText(
                            text: plan.duration,
                            fontSize: MySize.fontSize(12),
                            color: AppColorConstants.textSecondary,
                          ),
                        ] else ...[
                          AppText(
                            text: 'FREE',
                            fontSize: MySize.fontSize(20),
                            fontWeight: FontWeight.bold,
                            color: AppColorConstants.colorGreen,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),

                Space.height(16),

                // Features
                ...plan.features.map((feature) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: MySize.height(8)),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: MySize.height(16),
                          color: AppColorConstants.colorGreen,
                        ),
                        Space.width(8),
                        Expanded(
                          child: AppText(
                            text: feature,
                            fontSize: MySize.fontSize(14),
                            color: AppColorConstants.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  );
                }),

                Space.height(16),

                SizedBox(
                  width: double.infinity,
                  child: AppButton(
                    text: plan.isCurrentPlan
                        ? 'Current Plan'
                        : plan.id == 'free'
                        ? 'Downgrade to Free'
                        : 'Subscribe Now',
                    onPressed: plan.isCurrentPlan
                        ? null
                        : () => _subscriptionPageHelper!.subscribeToPlan(plan),
                    backgroundColor: plan.isCurrentPlan
                        ? AppColorConstants.cardColor
                        : plan.id == 'free'
                        ? AppColorConstants.colorRed
                        : AppColorConstants.primaryColor,
                    textColor: plan.isCurrentPlan
                        ? AppColorConstants.textSecondary
                        : AppColorConstants.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
