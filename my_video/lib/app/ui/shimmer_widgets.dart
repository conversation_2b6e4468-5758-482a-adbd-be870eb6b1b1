import 'package:my_video/app_imports.dart';

class ShimmerWidgets {
  // Base shimmer container
  static Widget _shimmerContainer({
    required double width,
    required double height,
    double borderRadius = 8.0,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: const Color(
          0xFF2A2A2A,
        ), // Slightly lighter than base shimmer color
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }

  // Shimmer wrapper
  static Widget _shimmerWrapper({required Widget child}) {
    return Shimmer.fromColors(
      baseColor: const Color(0xFF1A1A1A), // Darker base color
      highlightColor: const Color(0xFF3A3A3A), // Lighter highlight color
      period: const Duration(milliseconds: 1500),
      child: child,
    );
  }

  // Home page banner shimmer
  static Widget buildBannerShimmer() {
    return _shimmerWrapper(
      child: Container(
        height: MySize.height(220),
        margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
        child: Column(
          children: [
            Expanded(
              child: _shimmerContainer(
                width: double.infinity,
                height: MySize.height(200),
                borderRadius: 12.0,
              ),
            ),
            Space.height(12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                3,
                (index) => Container(
                  margin: EdgeInsets.symmetric(horizontal: MySize.width(4)),
                  child: _shimmerContainer(
                    width: MySize.width(8),
                    height: MySize.height(8),
                    borderRadius: 4.0,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Movie card shimmer
  static Widget buildMovieCardShimmer() {
    return _shimmerWrapper(
      child: Container(
        width: MySize.width(140),
        margin: EdgeInsets.only(right: MySize.width(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _shimmerContainer(
              width: MySize.width(140),
              height: MySize.height(180),
              borderRadius: 8.0,
            ),
            Space.height(8),
            _shimmerContainer(
              width: MySize.width(120),
              height: MySize.height(16),
              borderRadius: 4.0,
            ),
            Space.height(4),
            _shimmerContainer(
              width: MySize.width(80),
              height: MySize.height(12),
              borderRadius: 4.0,
            ),
          ],
        ),
      ),
    );
  }

  // Category section shimmer
  static Widget buildCategorySectionShimmer() {
    return _shimmerWrapper(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _shimmerContainer(
                  width: MySize.width(120),
                  height: MySize.height(20),
                  borderRadius: 4.0,
                ),
                _shimmerContainer(
                  width: MySize.width(60),
                  height: MySize.height(16),
                  borderRadius: 4.0,
                ),
              ],
            ),
          ),
          Space.height(12),
          SizedBox(
            height: MySize.height(240),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
              itemCount: 4,
              itemBuilder: (context, index) => buildMovieCardShimmer(),
            ),
          ),
          Space.height(24),
        ],
      ),
    );
  }

  // Home page full shimmer
  static Widget buildHomePageShimmer() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // App bar shimmer
          _shimmerWrapper(
            child: Padding(
              padding: EdgeInsets.all(MySize.width(16)),
              child: Row(
                children: [
                  _shimmerContainer(
                    width: MySize.width(100),
                    height: MySize.height(24),
                    borderRadius: 4.0,
                  ),
                  const Spacer(),
                  _shimmerContainer(
                    width: MySize.width(24),
                    height: MySize.height(24),
                    borderRadius: 12.0,
                  ),
                  Space.width(16),
                  _shimmerContainer(
                    width: MySize.width(24),
                    height: MySize.height(24),
                    borderRadius: 12.0,
                  ),
                ],
              ),
            ),
          ),

          // Banner shimmer
          buildBannerShimmer(),
          Space.height(24),

          // Category sections shimmer
          ...List.generate(3, (index) => buildCategorySectionShimmer()),

          Space.height(120), // Bottom padding
        ],
      ),
    );
  }

  // Filter chip shimmer
  static Widget buildFilterChipShimmer({double? width}) {
    return _shimmerWrapper(
      child: _shimmerContainer(
        width: width ?? MySize.width(80),
        height: MySize.height(32),
        borderRadius: 16.0,
      ),
    );
  }

  // Filter section shimmer
  static Widget buildFilterSectionShimmer() {
    return _shimmerWrapper(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _shimmerContainer(
            width: MySize.width(100),
            height: MySize.height(20),
            borderRadius: 4.0,
          ),
          Space.height(12),
          Wrap(
            spacing: MySize.width(8),
            runSpacing: MySize.height(8),
            children: List.generate(
              6,
              (index) => buildFilterChipShimmer(
                width: MySize.width(60 + (index % 3) * 20),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Filter bottom sheet shimmer
  static Widget buildFilterBottomSheetShimmer() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(MySize.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildFilterSectionShimmer(),
          Space.height(24),
          buildFilterSectionShimmer(),
          Space.height(24),
          buildFilterSectionShimmer(),
        ],
      ),
    );
  }

  // Grid movie card shimmer (for guest home page)
  static Widget buildGridMovieCardShimmer() {
    return _shimmerWrapper(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _shimmerContainer(
            width: double.infinity,
            height: MySize.height(200),
            borderRadius: 8.0,
          ),
          Space.height(8),
          _shimmerContainer(
            width: double.infinity,
            height: MySize.height(16),
            borderRadius: 4.0,
          ),
          Space.height(4),
          Row(
            children: [
              _shimmerContainer(
                width: MySize.width(40),
                height: MySize.height(12),
                borderRadius: 4.0,
              ),
              const Spacer(),
              _shimmerContainer(
                width: MySize.width(30),
                height: MySize.height(12),
                borderRadius: 4.0,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Guest home page shimmer (grid layout)
  static Widget buildGuestHomePageShimmer({bool showBanner = false}) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _shimmerWrapper(
            child: Padding(
              padding: EdgeInsets.all(MySize.width(16)),
              child: Row(
                children: [
                  _shimmerContainer(
                    width: MySize.width(100),
                    height: MySize.height(24),
                    borderRadius: 4.0,
                  ),
                  const Spacer(),
                  _shimmerContainer(
                    width: MySize.width(24),
                    height: MySize.height(24),
                    borderRadius: 12.0,
                  ),
                  Space.width(16),
                  _shimmerContainer(
                    width: MySize.width(24),
                    height: MySize.height(24),
                    borderRadius: 12.0,
                  ),
                ],
              ),
            ),
          ),

          if (showBanner) ...[buildBannerShimmer(), Space.height(24)],

          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _shimmerWrapper(
                  child: _shimmerContainer(
                    width: MySize.width(120),
                    height: MySize.height(20),
                    borderRadius: 4.0,
                  ),
                ),
                Space.height(16),

                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.6,
                    crossAxisSpacing: MySize.width(12),
                    mainAxisSpacing: MySize.height(16),
                  ),
                  itemCount: 8,
                  itemBuilder: (context, index) => buildGridMovieCardShimmer(),
                ),
              ],
            ),
          ),

          Space.height(120), // Bottom padding
        ],
      ),
    );
  }

  static Widget buildTextShimmer({
    double? width,
    double? height,
    double borderRadius = 4.0,
  }) {
    return _shimmerWrapper(
      child: _shimmerContainer(
        width: width ?? MySize.width(100),
        height: height ?? MySize.height(16),
        borderRadius: borderRadius,
      ),
    );
  }
}
