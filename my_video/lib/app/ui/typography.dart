import 'package:flutter_html/flutter_html.dart';
import 'package:my_video/app/ui/translation_service.dart';
import 'package:my_video/app_imports.dart';
import 'package:url_launcher/url_launcher_string.dart';

TextStyle myTextStyle({
  double? fontSize,
  Color? color,
  Color backgroundColor = AppColorConstants.colorPrimary,
  FontWeight? fontWeight,
  String? fontFamily,
  TextDecoration? textDecoration,
  FontStyle? fontStyle,
  BuildContext? context,
}) {
  final Color effectiveColor =
      color ??
      (context != null
          ? AppColorConstants.colorPrimary
          : AppColorConstants.colorPrimary);

  return GoogleFonts.inter(
    fontSize: fontSize ?? 12,
    color: effectiveColor,
    fontWeight: fontWeight ?? FontWeight.normal,
    decoration: textDecoration ?? TextDecoration.none,
    backgroundColor: backgroundColor,
    fontStyle: fontStyle,
  );
}

class AppText extends StatelessWidget {
  final String? htmlText;
  final String? text;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextStyle? textStyle;
  final FontStyle? fontStyle;
  final TextDecoration? textDecoration;
  final TextAlign? textAlign;
  final Color? color;
  final Color highlightTextColor;
  final Color backgroundColor;
  final int? maxLines;
  final TextOverflow? overflow;
  final List<String>? highlightKeywords;
  final List<String>? lineThroughWord;
  final List<String>? underLineWord;
  final List<String>? overLineWord;
  final bool isTranslateDisabled;
  final ShaderCallback? shaderCallback;

  const AppText({
    super.key,
    this.text,
    this.htmlText,
    this.fontWeight,
    this.fontSize,
    this.textStyle,
    this.fontStyle,
    this.textDecoration,
    this.color,
    this.highlightTextColor = Colors.yellow,
    this.backgroundColor = Colors.transparent,
    this.highlightKeywords,
    this.lineThroughWord,
    this.underLineWord,
    this.overLineWord,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.isTranslateDisabled = false,
    this.shaderCallback,
  });

  TextStyle myTextStyle({
    double? fontSize,
    Color? color,
    FontWeight? fontWeight,
    String? fontFamily,
    TextDecoration? textDecoration,
    FontStyle? fontStyle,
  }) {
    return GoogleFonts.inter(
      fontSize: fontSize ?? 12,
      color: color,
      fontWeight: fontWeight,
      decoration: textDecoration,
      fontStyle: fontStyle,
    );
  }

  @override
  Widget build(BuildContext context) {
    final TextStyle effectiveTextStyle =
        textStyle ??
        myTextStyle(
          fontSize: fontSize,
          color: color ?? AppColorConstants.colorPrimary,
          fontWeight: fontWeight,
          textDecoration: textDecoration,
          fontStyle: fontStyle,
        );

    if (highlightKeywords != null ||
        lineThroughWord != null ||
        underLineWord != null ||
        overLineWord != null) {
      return HighlightedMarkdown(
        text: text ?? '',
        highlightKeywords: highlightKeywords ?? [],
        textStyle: effectiveTextStyle,
        lineThroughWord: lineThroughWord ?? [],
        textDecoration: textDecoration,
        underLineWord: underLineWord ?? [],
        overLineWord: overLineWord ?? [],
        fontSize: fontSize,
        fontWeight: fontWeight,
        fontStyle: fontStyle,
        color: color,
      );
    }

    final String? contentToTranslate = text ?? htmlText;

    return FutureBuilder<String>(
      future: htmlText != null
          ? TranslationService().translateHtml(htmlText ?? '')
          : TranslationService().translate(contentToTranslate ?? ''),
      builder: (context, snapshot) {
        String translatedText = '';
        if (isTranslateDisabled || TranslationService().isDefault()) {
          translatedText = contentToTranslate ?? '';
        } else if (snapshot.connectionState == ConnectionState.done) {
          translatedText = snapshot.data ?? ' ' * translatedText.length;
        }
        if (htmlText != null) {
          return Html(
            data: translatedText,
            style: style(),
            onLinkTap: (url, attributes, element) {
              if (url != null) {
                launchUrlString(url.trim());
              }
            },
          );
        }

        if (shaderCallback != null) {
          return ShaderMask(
            shaderCallback: shaderCallback!,
            child: Text(
              translatedText,
              style: effectiveTextStyle.copyWith(color: Colors.white),
              // Neutral color for text
              textAlign: textAlign,
              maxLines: maxLines,
              overflow: overflow,
            ),
          );
        }

        return Text(
          translatedText,
          style: effectiveTextStyle,
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }
}

Map<String, Style> style() {
  // Use fixed values instead of MySize to avoid LateInitializationError
  return {
    'p': Style(
      lineHeight: const LineHeight(1.5),
      padding: HtmlPaddings.zero,
      fontSize: FontSize(14), // MySize.size14
      margin: Margins.zero,
    ),
    'h3': Style(
      padding: HtmlPaddings.only(top: 0.5),
      margin: Margins.zero,
      fontSize: FontSize(18), // MySize.size18
      fontWeight: FontWeight.w600,
    ),
    'h4': Style(
      padding: HtmlPaddings.only(bottom: 0, top: 0.9, right: 0, left: 0),
      margin: Margins.zero,
      fontSize: FontSize(16), // MySize.size16
      fontWeight: FontWeight.w600,
    ),
    'h2': Style(
      padding: HtmlPaddings.only(bottom: 0, top: 0.9, right: 0, left: 0),
      margin: Margins.zero,
      fontSize: FontSize(20), // MySize.size20
      fontWeight: FontWeight.w600,
    ),
    'h1': Style(
      padding: HtmlPaddings.only(bottom: 0, top: 0.9, right: 0, left: 0),
      margin: Margins.zero,
      fontSize: FontSize(22), // MySize.size22
      fontWeight: FontWeight.w800,
    ),
    'p span': Style(
      lineHeight: const LineHeight(1.5),
      padding: HtmlPaddings.zero,
      fontSize: FontSize(14), // MySize.size14
      margin: Margins.zero,
    ),
    'br': Style(
      height: Height(5),
      margin: Margins.zero,
      lineHeight: const LineHeight(0.1),
    ),
  };
}

class HighlightedMarkdown extends StatelessWidget {
  final String text;
  final TextStyle? textStyle;
  final FontWeight? fontWeight;
  final double? fontSize;
  final FontStyle? fontStyle;
  final TextDecoration? textDecoration;
  final TextAlign? textAlign;
  final Color? color;
  final Color highlightTextColor;
  final Color backgroundColor;
  final List<String> highlightKeywords;
  final List<String> lineThroughWord;
  final List<String> underLineWord;
  final List<String> overLineWord;

  const HighlightedMarkdown({
    super.key,
    required this.text,
    this.textStyle,
    this.fontWeight,
    this.fontSize,
    this.fontStyle,
    this.textDecoration,
    this.color,
    this.highlightTextColor = AppColorConstants.colorPrimary,
    this.backgroundColor = AppColorConstants.colorPrimary,
    this.highlightKeywords = const [],
    this.lineThroughWord = const [],
    this.underLineWord = const [],
    this.overLineWord = const [],
    this.textAlign,
  });

  @override
  Widget build(BuildContext context) {
    final textSpans = _parseMarkdown(text, highlightKeywords);
    return RichText(
      text: TextSpan(children: textSpans),
      textAlign: textAlign ?? TextAlign.start,
    );
  }

  List<TextSpan> _parseMarkdown(String text, List<String> highlightKeywords) {
    final List<TextSpan> spans = [];

    int start = 0;

    for (int i = 0; i < text.length; i++) {
      final List<TextDecoration> decorations = [];
      final bool isHighLight = (highlightKeywords).contains(
        text.substring(start, i + 1),
      );

      Color textColor = color ?? AppColorConstants.colorPrimary;
      TextDecoration? textDecoration = this.textDecoration;
      Color backgroundColor = AppColorConstants.colorPrimary;
      if (isHighLight) {
        textColor = highlightTextColor;
        backgroundColor = this.backgroundColor;
      }

      if (textDecoration != null) {
        decorations.add(textDecoration);
      }

      if (underLineWord.contains(text.substring(start, i + 1))) {
        decorations.add(TextDecoration.underline);
      }

      if (overLineWord.contains(text.substring(start, i + 1))) {
        decorations.add(TextDecoration.overline);
      }

      if (lineThroughWord.contains(text.substring(start, i + 1))) {
        decorations.add(TextDecoration.lineThrough);
      }

      if (decorations.isNotEmpty) {
        textDecoration = TextDecoration.combine(decorations);
      }

      final TextStyle highlightTextStyle = GoogleFonts.inter(
        fontSize: fontSize ?? 12,
        color: textColor,
        backgroundColor: backgroundColor,
        fontWeight: fontWeight,
        decoration: textDecoration,
        fontStyle: fontStyle,
      );

      if (text[i] == ' ') {
        spans.add(TextSpan(text: text.substring(start, i), style: textStyle));
        spans.add(TextSpan(text: ' ', style: textStyle)); // Add space here
        start = i + 1;
      } else if ([
        ...overLineWord,
        ...lineThroughWord,
        ...underLineWord,
        ...highlightKeywords,
      ].contains(text.substring(start, i + 1))) {
        spans.add(
          TextSpan(
            text: text.substring(start, i + 1),
            style: highlightTextStyle,
          ),
        );
        start = i + 1;
      }
    }

    if (start < text.length) {
      spans.add(TextSpan(text: '${text.substring(start)} ', style: textStyle));
    }
    return spans;
  }
}
