import 'package:translator/translator.dart';
import 'package:my_video/app_imports.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;

String selectedCountryCode =
    AppSharedPreference.getString('selectedCountryCode') ?? 'US';
String selectedLanguageCode =
    AppSharedPreference.getString('selectedLanguageCode') ?? 'en';

Map<String, dynamic> defaultWord = {
  'hi': {'Hindi': 'हिंदी'},
};

class TranslationService extends GetxController {
  TranslationService._();

  static final TranslationService _instance = TranslationService._();

  factory TranslationService() => _instance;

  final Rx<Locale> _locale = Locale(
    selectedLanguageCode,
    selectedCountryCode,
  ).obs;

  Locale get locale => _locale.value;

  void setLocale(Locale locale) {
    _locale.value = locale;
    Get.updateLocale(locale);
  }

  bool isDefault() {
    return _locale.value.countryCode == 'US' &&
        _locale.value.languageCode == 'en';
  }

  Future<String> translate(String text) async {
    if (text == '') return text;

    if (_locale.value.countryCode == 'US' &&
        _locale.value.languageCode == 'en') {
      return text;
    }
    final String? isDefaultWord =
        defaultWord[_locale.value.languageCode]?[text];
    if (isDefaultWord != null) return isDefaultWord;

    final GoogleTranslator translator = GoogleTranslator();
    try {
      final translation = await translator.translate(
        text,
        to: locale.languageCode,
      );
      defaultWord[locale.languageCode][text] = translation.text;
      return translation.text;
    } catch (e) {
      return text;
    }
  }

  Future<String> translateHtml(String htmlContent) async {
    if (htmlContent.isEmpty) return htmlContent;

    final dom.Document document = html_parser.parse(htmlContent);
    final List<dom.Text> textNodes = [];

    void collectTextNodes(dom.Node node) {
      if (node is dom.Text) {
        textNodes.add(node);
      } else if (node is dom.Element) {
        node.nodes.forEach(collectTextNodes);
      }
    }

    document.body?.nodes.forEach(collectTextNodes);

    final List<String> textsToTranslate = textNodes
        .map((node) => node.text)
        .toList();
    final List<String> translatedTexts = await translateBatch(textsToTranslate);
    for (int i = 0; i < textNodes.length; i++) {
      textNodes[i].text = translatedTexts[i];
    }

    return document.body?.outerHtml ?? htmlContent;
  }

  Future<List<String>> translateBatch(List<String> texts) async {
    return Future.wait(texts.map((text) async => await translate(text)));
  }
}
