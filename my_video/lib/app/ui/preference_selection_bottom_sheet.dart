import 'package:my_video/app_imports.dart';

class PreferenceSelectionBottomSheet extends StatefulWidget {
  final SelectedFilters? initialFilters;
  final Function(SelectedFilters)? onFiltersSelected;
  final bool showApplyButton;
  final String title;

  const PreferenceSelectionBottomSheet({
    super.key,
    this.initialFilters,
    this.onFiltersSelected,
    this.showApplyButton = true,
    this.title = 'Select Your Preferences',
  });

  @override
  State<PreferenceSelectionBottomSheet> createState() =>
      _PreferenceSelectionBottomSheetState();
}

class _PreferenceSelectionBottomSheetState
    extends State<PreferenceSelectionBottomSheet> {
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();

  bool _isLoading = true;
  List<FilterLanguage> _availableLanguages = [];
  List<FilterGenre> _availableGenres = [];
  List<FilterCategory> _availableCategories = [];

  List<FilterLanguage> _selectedLanguages = [];
  List<FilterGenre> _selectedGenres = [];
  List<FilterCategory> _selectedCategories = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      // Load available filters from API
      final filterResponse = await _movieRepository.getFilters();

      setState(() {
        _availableLanguages = filterResponse.languages;
        _availableGenres = filterResponse.genres;
        _availableCategories = filterResponse.categories;
      });

      // Set initial selections
      if (widget.initialFilters != null) {
        setState(() {
          _selectedLanguages = List.from(
            widget.initialFilters!.selectedLanguages,
          );
          _selectedGenres = List.from(widget.initialFilters!.selectedGenres);
          _selectedCategories = List.from(
            widget.initialFilters!.selectedCategories,
          );
        });
      } else {
        // Load from user preferences
        final savedFilters = await UserPreferenceService.instance
            .getSelectedFilters();
        setState(() {
          _selectedLanguages = List.from(savedFilters.selectedLanguages);
          _selectedGenres = List.from(savedFilters.selectedGenres);
          _selectedCategories = List.from(savedFilters.selectedCategories);
        });
      }
    } catch (e) {
      AppHelper.logDebug('Error loading filters: $e');
      // Set default values if API fails
      setState(() {
        _availableLanguages = [
          FilterLanguage(langId: 1, language: 'English'),
          FilterLanguage(langId: 2, language: 'हिंदी'),
          FilterLanguage(langId: 3, language: 'ગુજરાતી'),
        ];
        _availableGenres = [
          FilterGenre(genreId: 3, genre: 'Bollywood'),
          FilterGenre(genreId: 4, genre: 'Hollywood'),
          FilterGenre(genreId: 2, genre: 'Tollywood'),
        ];
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _toggleLanguage(FilterLanguage language) {
    setState(() {
      if (_selectedLanguages.contains(language)) {
        _selectedLanguages.remove(language);
      } else {
        _selectedLanguages.add(language);
      }
    });
  }

  void _toggleGenre(FilterGenre genre) {
    setState(() {
      if (_selectedGenres.contains(genre)) {
        _selectedGenres.remove(genre);
      } else {
        _selectedGenres.add(genre);
      }
    });
  }

  void _toggleCategory(FilterCategory category) {
    setState(() {
      if (_selectedCategories.contains(category)) {
        _selectedCategories.remove(category);
      } else {
        _selectedCategories.add(category);
      }
    });
  }

  Future<void> _applyFilters() async {
    final selectedFilters = SelectedFilters(
      selectedLanguages: _selectedLanguages,
      selectedGenres: _selectedGenres,
      selectedCategories: _selectedCategories,
    );

    // Save to preferences
    await UserPreferenceService.instance.saveSelectedFilters(selectedFilters);

    // Callback to parent
    if (widget.onFiltersSelected != null) {
      widget.onFiltersSelected!(selectedFilters);
    }

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MySize.height(600),
      decoration: BoxDecoration(
        color: AppColorConstants.backgroundColor,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(MySize.width(20)),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: MySize.width(40),
            height: MySize.height(4),
            margin: EdgeInsets.symmetric(vertical: MySize.height(12)),
            decoration: BoxDecoration(
              color: AppColorConstants.colorGrey,
              borderRadius: BorderRadius.circular(MySize.width(2)),
            ),
          ),

          // Title
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.width(20)),
            child: Row(
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    color: AppColorConstants.colorWhite,
                    fontSize: MySize.width(18),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: AppColorConstants.colorWhite,
                    size: MySize.width(24),
                  ),
                ),
              ],
            ),
          ),

          if (_isLoading)
            Expanded(
              child: Center(
                child: CircularProgressIndicator(
                  color: AppColorConstants.primaryColor,
                ),
              ),
            )
          else
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: MySize.width(20)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Languages Section
                    _buildSectionTitle('Languages'),
                    Space.height(12),
                    _buildLanguageChips(),
                    Space.height(24),

                    // Genres Section
                    _buildSectionTitle('Genres'),
                    Space.height(12),
                    _buildGenreChips(),
                    Space.height(24),

                    // Categories Section (optional)
                    if (_availableCategories.isNotEmpty) ...[
                      _buildSectionTitle('Categories'),
                      Space.height(12),
                      _buildCategoryChips(),
                      Space.height(24),
                    ],

                    Space.height(80), // Bottom padding for apply button
                  ],
                ),
              ),
            ),

          // Apply Button
          if (widget.showApplyButton)
            Container(
              padding: EdgeInsets.all(MySize.width(20)),
              child: SizedBox(
                width: double.infinity,
                height: MySize.height(48),
                child: ElevatedButton(
                  onPressed: _applyFilters,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColorConstants.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.width(8)),
                    ),
                  ),
                  child: Text(
                    'Apply Preferences',
                    style: TextStyle(
                      color: AppColorConstants.colorWhite,
                      fontSize: MySize.width(16),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        color: AppColorConstants.colorWhite,
        fontSize: MySize.width(16),
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildLanguageChips() {
    return Wrap(
      spacing: MySize.width(8),
      runSpacing: MySize.height(8),
      children: _availableLanguages.map((language) {
        final isSelected = _selectedLanguages.contains(language);
        return GestureDetector(
          onTap: () => _toggleLanguage(language),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.width(16),
              vertical: MySize.height(8),
            ),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.width(20)),
              border: Border.all(
                color: isSelected
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.colorGrey,
                width: 1,
              ),
            ),
            child: Text(
              language.language,
              style: TextStyle(
                color: isSelected
                    ? AppColorConstants.colorWhite
                    : AppColorConstants.colorGrey,
                fontSize: MySize.width(14),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGenreChips() {
    return Wrap(
      spacing: MySize.width(8),
      runSpacing: MySize.height(8),
      children: _availableGenres.map((genre) {
        final isSelected = _selectedGenres.contains(genre);
        return GestureDetector(
          onTap: () => _toggleGenre(genre),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.width(16),
              vertical: MySize.height(8),
            ),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.width(20)),
              border: Border.all(
                color: isSelected
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.colorGrey,
                width: 1,
              ),
            ),
            child: Text(
              genre.genre,
              style: TextStyle(
                color: isSelected
                    ? AppColorConstants.colorWhite
                    : AppColorConstants.colorGrey,
                fontSize: MySize.width(14),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCategoryChips() {
    return Wrap(
      spacing: MySize.width(8),
      runSpacing: MySize.height(8),
      children: _availableCategories.map((category) {
        final isSelected = _selectedCategories.contains(category);
        return GestureDetector(
          onTap: () => _toggleCategory(category),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.width(16),
              vertical: MySize.height(8),
            ),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.width(20)),
              border: Border.all(
                color: isSelected
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.colorGrey,
                width: 1,
              ),
            ),
            child: Text(
              category.catName,
              style: TextStyle(
                color: isSelected
                    ? AppColorConstants.colorWhite
                    : AppColorConstants.colorGrey,
                fontSize: MySize.width(14),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}

// Helper function to show the bottom sheet
void showPreferenceSelectionBottomSheet(
  BuildContext context, {
  SelectedFilters? initialFilters,
  Function(SelectedFilters)? onFiltersSelected,
  bool showApplyButton = true,
  String title = 'Select Your Preferences',
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => PreferenceSelectionBottomSheet(
      initialFilters: initialFilters,
      onFiltersSelected: onFiltersSelected,
      showApplyButton: showApplyButton,
      title: title,
    ),
  );
}
