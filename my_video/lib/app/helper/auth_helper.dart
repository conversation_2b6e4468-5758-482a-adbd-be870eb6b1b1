import 'package:my_video/app_imports.dart';

class AuthHelper {
  static final Logger _logger = Logger();

  /// Check if user is logged in
  static bool get isLoggedIn {
    final token = AppSharedPreference.getUserToken();
    final userData = AppSharedPreference.getUserData();
    return token != null && token.isNotEmpty && userData != null;
  }

  /// Check if user has unregistered token (can access basic features)
  static bool get hasUnregisteredAccess {
    final staticToken = AppSharedPreference.getString('static_token');
    return staticToken != null && staticToken.isNotEmpty;
  }

  /// Get current user data
  static Map<String, dynamic>? get currentUser {
    return AppSharedPreference.getUserData();
  }

  /// Get current user token
  static String? get currentUserToken {
    return AppSharedPreference.getUserToken();
  }

  /// Get unregistered token
  static String? get unregisteredToken {
    return AppSharedPreference.getString('static_token');
  }

  /// Show login required dialog
  static void showLoginRequiredDialog(
    BuildContext context, {
    String? title,
    String? message,
    VoidCallback? onLoginPressed,
  }) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColorConstants.cardColor,
        title: AppText(
          text: title ?? 'Login Required',
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        content: AppText(
          text: message ?? 'You need to login to access this feature.',
          fontSize: 14,
          color: AppColorConstants.textSecondary,
        ),
        actions: [
          TextButton(
            onPressed: () => gotoBack(),
            child: AppText(
              text: 'Cancel',
              fontSize: 14,
              color: AppColorConstants.textSecondary,
            ),
          ),
          AppButton(
            text: 'Login',
            onPressed: () {
              gotoBack();
              if (onLoginPressed != null) {
                onLoginPressed();
              } else {
                gotoGoogleLoginPage();
              }
            },
            backgroundColor: AppColorConstants.primaryColor,
            padding: EdgeInsets.symmetric(
              horizontal: MySize.width(16),
              vertical: MySize.height(8),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// Show login required bottom sheet
  static void showLoginRequiredBottomSheet(
    BuildContext context, {
    String? title,
    String? message,
    VoidCallback? onLoginPressed,
  }) {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(MySize.width(24)),
        decoration: const BoxDecoration(
          color: AppColorConstants.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: MySize.width(40),
              height: MySize.height(4),
              decoration: BoxDecoration(
                color: AppColorConstants.dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Space.height(20),

            // Icon
            Icon(
              Icons.login,
              size: MySize.height(48),
              color: AppColorConstants.primaryColor,
            ),
            Space.height(16),

            // Title
            AppText(
              text: title ?? 'Login Required',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
              textAlign: TextAlign.center,
            ),
            Space.height(8),

            // Message
            AppText(
              text:
                  message ??
                  'You need to login to access this feature. Create an account or login to continue.',
              fontSize: 14,
              color: AppColorConstants.textSecondary,
              textAlign: TextAlign.center,
            ),
            Space.height(24),

            // Buttons
            Row(
              children: [
                Expanded(
                  child: AppButton(
                    text: 'Cancel',
                    onPressed: () => gotoBack(),
                    backgroundColor: AppColorConstants.cardColor,
                    textColor: AppColorConstants.textSecondary,
                  ),
                ),
                Space.width(12),
                Expanded(
                  child: AppButton(
                    text: 'Login',
                    onPressed: () {
                      gotoBack();
                      if (onLoginPressed != null) {
                        onLoginPressed();
                      } else {
                        gotoGoogleLoginPage();
                      }
                    },
                    backgroundColor: AppColorConstants.primaryColor,
                  ),
                ),
              ],
            ),
            Space.height(16),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  /// Check if feature requires login and show appropriate UI
  static bool checkLoginAndShowDialog(
    BuildContext context, {
    String? title,
    String? message,
    VoidCallback? onLoginPressed,
    bool useBottomSheet = true,
  }) {
    if (!isLoggedIn) {
      if (useBottomSheet) {
        showLoginRequiredBottomSheet(
          context,
          title: title,
          message: message,
          onLoginPressed: onLoginPressed,
        );
      } else {
        showLoginRequiredDialog(
          context,
          title: title,
          message: message,
          onLoginPressed: onLoginPressed,
        );
      }
      return false;
    }
    return true;
  }

  /// Logout user
  static Future<void> logout() async {
    try {
      _logger.i('Logging out user...');

      // Clear user data using AppSharedPreference logout method
      await AppSharedPreference.logout();

      // Keep unregistered token for basic access
      // Don't clear static_token as it's needed for home page access

      _logger.i('User logged out successfully');

      // Navigate to login page
      gotoLoginPage();

      AppHelper.showToast('Logged out successfully');
    } catch (e) {
      _logger.e('Error during logout: $e');
      AppHelper.showToast('Error during logout', isError: true);
    }
  }

  /// Check if user can access feature without login
  static bool canAccessWithoutLogin(String feature) {
    // Features that can be accessed without login
    const publicFeatures = ['home', 'search', 'movie_player', 'movie_details'];

    return publicFeatures.contains(feature.toLowerCase());
  }

  /// Check if user needs login for feature
  static bool requiresLogin(String feature) {
    // Features that require login
    const loginRequiredFeatures = [
      'settings',
      'playlist',
      'add_movie',
      'upload_movie',
      'profile',
      'subscription',
      'favorites',
      'watchlist',
    ];

    return loginRequiredFeatures.contains(feature.toLowerCase());
  }
}
