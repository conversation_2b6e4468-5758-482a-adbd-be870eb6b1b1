import 'package:my_video/app_imports.dart';

final GoRouter router = GoRouter(
  initialLocation: AppRoutes.splash,
  routes: [
    // Authentication Routes
    GoRoute(
      path: AppRoutes.splash,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: SplashPage()),
    ),
    GoRoute(
      path: AppRoutes.login,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: LoginPage()),
    ),
    GoRoute(
      path: AppRoutes.googleLogin,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: GoogleLoginPage()),
    ),
    GoRoute(
      path: AppRoutes.signup,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: SignupPage()),
    ),
    GoRoute(
      path: AppRoutes.forgotPassword,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: ForgotPasswordPage()),
    ),
    GoRoute(
      path: AppRoutes.initialSetup,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: InitialSetupPage()),
    ),

    // Main App Routes
    GoRoute(
      path: AppRoutes.mainNavigation,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: MainNavigationPage()),
    ),
    GoRoute(
      path: AppRoutes.noInternet,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: NoInternetConnectionPage()),
    ),

    // Movie Related Routes
    GoRoute(
      path: AppRoutes.moviePlayer,
      pageBuilder: (context, state) {
        final movie = state.extra as MovieModel;
        return NoTransitionPage(child: VideoPlayerPage(movie: movie));
      },
    ),

    // User Profile & Settings Routes
    GoRoute(
      path: AppRoutes.profile,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: ProfilePage()),
    ),
    GoRoute(
      path: AppRoutes.termsOfService,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: TermsOfServicePage()),
    ),
    GoRoute(
      path: AppRoutes.privacyPolicy,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: PrivacyPolicyPage()),
    ),
    GoRoute(
      path: AppRoutes.contactSupport,
      pageBuilder: (context, state) =>
          const NoTransitionPage(child: ContactSupportPage()),
    ),
  ],
  errorBuilder: (context, state) {
    MySize.init(context);
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: MySize.size64,
              color: AppColorConstants.colorRed,
            ),
            Space.height(16),
            AppText(
              text: 'Page not found',
              fontSize: MySize.fontSize(20),
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            Space.height(8),
            AppText(
              text: 'The page you are looking for does not exist.',
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.textSecondary,
            ),
            Space.height(24),
            AppButton(
              text: 'Go Home',
              onPressed: () => context.go(AppRoutes.splash),
            ),
          ],
        ),
      ),
    );
  },
);
