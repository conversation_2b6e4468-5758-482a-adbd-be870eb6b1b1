import 'package:my_video/app_imports.dart';

/// Service that provides static filter data for user preference selection
/// This is used when the API is not available or requires authentication
class StaticFilterService {
  static const List<String> _languages = [
    "English",
    "Español",
    "Português",
    "Deutsch",
    "Français",
    "عربي",
    "हिन्दी",
    "Chinese",
    "ગુજરાતી",
    "Korean",
    "తెలుగు",
    "മലയാളം",
    "ਪੰਜਾਬੀ",
    "বাংলা",
    "मराठी",
    "ಕನ್ನಡ",
    "தமிழ்",
    "Other"
  ];

  static const List<String> _categories = [
    "Action",
    "Popular",
    "Horror",
    "Drama",
    "Adventure",
    "Thriller",
    "Comedy",
    "War",
    "Romance",
    "Crime",
    "Sci-fi",
    "Mystery",
    "Fantasy",
    "Love Story",
    "Sport",
    "Family",
    "History",
    "Biography",
    "Political",
    "Animation",
    "Dance",
    "Documentary",
    "Western",
    "Musical",
    "Reality",
    "Exclusive Drama"
  ];

  static const List<String> _genres = [
    "Hollywood",
    "Spanish Cinema",
    "Portugal Cinema",
    "German Cinema",
    "French Cinema",
    "Arab Cinema",
    "Chinese Cinema",
    "Bollywood",
    "Gollywood",
    "Korean Cinema",
    "Tollywood",
    "Pollywood",
    "Sandalwood",
    "Mollywood",
    "Marathi Movies",
    "Jollywood",
    "Dhallywood",
    "Kollywood"
  ];

  /// Get static filter response for initial app setup
  static FilterResponse getStaticFilters() {
    return FilterResponse(
      status: 1,
      message: "Static filters loaded",
      categoryNames: List.from(_categories),
      languageNames: List.from(_languages),
      genreNames: List.from(_genres),
    );
  }

  /// Get default preferences for new users (English + Bollywood)
  static SelectedFilters getDefaultPreferences() {
    return SelectedFilters(
      selectedLanguages: [
        FilterLanguage(langId: 1, language: 'English'),
      ],
      selectedGenres: [
        FilterGenre(genreId: 8, genre: 'Bollywood'), // Index 7 in the list
      ],
      selectedCategories: [],
    );
  }

  /// Check if a language exists in the static list
  static bool isValidLanguage(String language) {
    return _languages.contains(language);
  }

  /// Check if a genre exists in the static list
  static bool isValidGenre(String genre) {
    return _genres.contains(genre);
  }

  /// Check if a category exists in the static list
  static bool isValidCategory(String category) {
    return _categories.contains(category);
  }

  /// Get language by name
  static FilterLanguage? getLanguageByName(String name) {
    final index = _languages.indexOf(name);
    if (index != -1) {
      return FilterLanguage(langId: index + 1, language: name);
    }
    return null;
  }

  /// Get genre by name
  static FilterGenre? getGenreByName(String name) {
    final index = _genres.indexOf(name);
    if (index != -1) {
      return FilterGenre(genreId: index + 1, genre: name);
    }
    return null;
  }

  /// Get category by name
  static FilterCategory? getCategoryByName(String name) {
    final index = _categories.indexOf(name);
    if (index != -1) {
      return FilterCategory(catId: index + 1, catName: name);
    }
    return null;
  }
}
