import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_video/model/filter/filter_model.dart';
import 'package:my_video/app/services/static_filter_service.dart';

class UserPreferenceService {
  static const String _keySelectedLanguages = 'selected_languages';
  static const String _keySelectedGenres = 'selected_genres';
  static const String _keySelectedCategories = 'selected_categories';
  static const String _keyIsFirstLaunch = 'is_first_launch';
  static const String _keyPreferencesSet = 'preferences_set';

  static UserPreferenceService? _instance;
  static UserPreferenceService get instance {
    _instance ??= UserPreferenceService._();
    return _instance!;
  }

  UserPreferenceService._();

  SharedPreferences? _prefs;

  Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Check if this is the first app launch
  Future<bool> isFirstLaunch() async {
    await _initPrefs();
    return _prefs!.getBool(_keyIsFirstLaunch) ?? true;
  }

  // Mark first launch as completed
  Future<void> setFirstLaunchCompleted() async {
    await _initPrefs();
    await _prefs!.setBool(_keyIsFirstLaunch, false);
  }

  // Check if user has set preferences
  Future<bool> hasSetPreferences() async {
    await _initPrefs();
    return _prefs!.getBool(_keyPreferencesSet) ?? false;
  }

  // Mark preferences as set
  Future<void> setPreferencesCompleted() async {
    await _initPrefs();
    await _prefs!.setBool(_keyPreferencesSet, true);
  }

  // Save selected languages
  Future<void> saveSelectedLanguages(List<FilterLanguage> languages) async {
    await _initPrefs();
    final languageJsonList = languages.map((lang) => lang.toJson()).toList();
    final jsonString = jsonEncode(languageJsonList);
    await _prefs!.setString(_keySelectedLanguages, jsonString);
  }

  // Get selected languages
  Future<List<FilterLanguage>> getSelectedLanguages() async {
    await _initPrefs();
    final jsonString = _prefs!.getString(_keySelectedLanguages);
    if (jsonString == null || jsonString.isEmpty) {
      return [];
    }

    try {
      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList
          .map((json) => FilterLanguage.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Save selected genres
  Future<void> saveSelectedGenres(List<FilterGenre> genres) async {
    await _initPrefs();
    final genreJsonList = genres.map((genre) => genre.toJson()).toList();
    final jsonString = jsonEncode(genreJsonList);
    await _prefs!.setString(_keySelectedGenres, jsonString);
  }

  // Get selected genres
  Future<List<FilterGenre>> getSelectedGenres() async {
    await _initPrefs();
    final jsonString = _prefs!.getString(_keySelectedGenres);
    if (jsonString == null || jsonString.isEmpty) {
      return [];
    }

    try {
      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList
          .map((json) => FilterGenre.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Save selected categories
  Future<void> saveSelectedCategories(List<FilterCategory> categories) async {
    await _initPrefs();
    final categoryJsonList = categories.map((cat) => cat.toJson()).toList();
    final jsonString = jsonEncode(categoryJsonList);
    await _prefs!.setString(_keySelectedCategories, jsonString);
  }

  // Get selected categories
  Future<List<FilterCategory>> getSelectedCategories() async {
    await _initPrefs();
    final jsonString = _prefs!.getString(_keySelectedCategories);
    if (jsonString == null || jsonString.isEmpty) {
      return [];
    }

    try {
      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList
          .map((json) => FilterCategory.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Get all selected filters as SelectedFilters object
  Future<SelectedFilters> getSelectedFilters() async {
    final languages = await getSelectedLanguages();
    final genres = await getSelectedGenres();
    final categories = await getSelectedCategories();

    return SelectedFilters(
      selectedLanguages: languages,
      selectedGenres: genres,
      selectedCategories: categories,
    );
  }

  // Save all selected filters
  Future<void> saveSelectedFilters(SelectedFilters filters) async {
    await Future.wait([
      saveSelectedLanguages(filters.selectedLanguages),
      saveSelectedGenres(filters.selectedGenres),
      saveSelectedCategories(filters.selectedCategories),
    ]);
    await setPreferencesCompleted();
  }

  // Clear all preferences
  Future<void> clearAllPreferences() async {
    await _initPrefs();
    await Future.wait([
      _prefs!.remove(_keySelectedLanguages),
      _prefs!.remove(_keySelectedGenres),
      _prefs!.remove(_keySelectedCategories),
      _prefs!.remove(_keyPreferencesSet),
    ]);
  }

  // Get language names as comma-separated string for API
  Future<String?> getLanguageNamesForAPI() async {
    final languages = await getSelectedLanguages();
    if (languages.isEmpty) return null;
    return languages.map((lang) => lang.language).join(',');
  }

  // Get genre names as comma-separated string for API
  Future<String?> getGenreNamesForAPI() async {
    final genres = await getSelectedGenres();
    if (genres.isEmpty) return null;
    return genres.map((genre) => genre.genre).join(',');
  }

  // Get category names as comma-separated string for API
  Future<String?> getCategoryNamesForAPI() async {
    final categories = await getSelectedCategories();
    if (categories.isEmpty) return null;
    return categories.map((cat) => cat.catName).join(',');
  }

  // Check if user has any preferences set
  Future<bool> hasAnyPreferences() async {
    final filters = await getSelectedFilters();
    return filters.hasAnyFilter;
  }

  // Get default preferences (English + Bollywood for new users)
  static SelectedFilters getDefaultPreferences() {
    // Use StaticFilterService for consistent default preferences
    return StaticFilterService.getDefaultPreferences();
  }
}
