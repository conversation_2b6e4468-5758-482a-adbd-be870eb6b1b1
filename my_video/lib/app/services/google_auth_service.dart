import 'package:my_video/app_imports.dart';

/// Service for handling Google Sign-In authentication
class GoogleAuthService {
  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
  );

  static final Logger _logger = Logger();

  /// Sign in with Google
  static Future<GoogleSignInAccount?> signInWithGoogle() async {
    try {
      _logger.i('Starting Google Sign-In...');
      
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser != null) {
        _logger.i('Google Sign-In successful for: ${googleUser.email}');
        return googleUser;
      } else {
        _logger.w('Google Sign-In was cancelled by user');
        return null;
      }
    } catch (error) {
      _logger.e('Google Sign-In error: $error');
      return null;
    }
  }

  /// Sign out from Google
  static Future<void> signOut() async {
    try {
      _logger.i('Signing out from Google...');
      await _googleSignIn.signOut();
      _logger.i('Google Sign-Out successful');
    } catch (error) {
      _logger.e('Google Sign-Out error: $error');
    }
  }

  /// Get current signed-in Google user
  static GoogleSignInAccount? getCurrentUser() {
    return _googleSignIn.currentUser;
  }

  /// Check if user is signed in with Google
  static bool isSignedIn() {
    return _googleSignIn.currentUser != null;
  }

  /// Get Google user authentication details
  static Future<GoogleSignInAuthentication?> getGoogleAuth(
    GoogleSignInAccount googleUser,
  ) async {
    try {
      return await googleUser.authentication;
    } catch (error) {
      _logger.e('Error getting Google authentication: $error');
      return null;
    }
  }

  /// Extract user data from Google account for API calls
  static Map<String, dynamic> extractUserData(GoogleSignInAccount googleUser) {
    final nameParts = googleUser.displayName?.split(' ') ?? ['', ''];
    final firstName = nameParts.isNotEmpty ? nameParts[0] : '';
    final lastName = nameParts.length > 1 
        ? nameParts.sublist(1).join(' ') 
        : '';

    return {
      'email': googleUser.email,
      'fname': firstName,
      'lname': lastName,
      'gender': 'm', // Default gender, can be updated later
      'photoUrl': googleUser.photoUrl,
      'googleId': googleUser.id,
    };
  }

  /// Silent sign-in (try to sign in without user interaction)
  static Future<GoogleSignInAccount?> signInSilently() async {
    try {
      _logger.i('Attempting silent Google Sign-In...');
      final GoogleSignInAccount? googleUser = await _googleSignIn.signInSilently();
      
      if (googleUser != null) {
        _logger.i('Silent Google Sign-In successful for: ${googleUser.email}');
        return googleUser;
      } else {
        _logger.i('No previous Google Sign-In found');
        return null;
      }
    } catch (error) {
      _logger.e('Silent Google Sign-In error: $error');
      return null;
    }
  }

  /// Disconnect from Google (revoke access)
  static Future<void> disconnect() async {
    try {
      _logger.i('Disconnecting from Google...');
      await _googleSignIn.disconnect();
      _logger.i('Google disconnect successful');
    } catch (error) {
      _logger.e('Google disconnect error: $error');
    }
  }
}
