import 'package:my_video/app_imports.dart';

abstract class MovieRepository {
  // Only using showcatwise API - removed getAllCategories and getFeaturedMovies
  Future<CategoryWiseResponse> getCategoryWiseMovies({
    int page = 1,
    String? languages,
    String? genres,
    String? categories,
    String? isMovie,
  });

  // Show posts API for guest users (non-logged-in)
  Future<ShowPostsResponse> getShowPosts({
    int page = 1,
    String? languages,
    String? genres,
    String? categories,
    String? isMovie,
  });

  Future<SearchMoviesResponse> searchMovies(
    String query, {
    int page = 1,
    int perPage = 20,
  });

  // New search method for authenticated users using /searchpost/:page endpoint
  Future<ShowPostsResponse> searchPosts(
    String searchQuery, {
    int page = 1,
    String? isMovie,
    String? languages,
    String? genres,
    String? categories,
  });
  Future<MovieModel?> getMovieById(String id);
  Future<List<MovieModel>> getRelatedMovies(String movieId, String category);
  Future<FilterResponse> getFilters();
}

class MovieRepositoryImpl implements MovieRepository {
  final Logger _logger = Logger();

  @override
  Future<CategoryWiseResponse> getCategoryWiseMovies({
    int page = 1,
    String? languages,
    String? genres,
    String? categories,
    String? isMovie,
  }) async {
    try {
      // Prepare request body with filters
      final Map<String, dynamic> requestBody = {};

      if (languages != null && languages.isNotEmpty) {
        requestBody['lang'] = languages;
      }
      if (genres != null && genres.isNotEmpty) {
        requestBody['genre'] = genres;
      }
      if (categories != null && categories.isNotEmpty) {
        requestBody['cat'] = categories;
      }
      if (isMovie != null && isMovie.isNotEmpty) {
        requestBody['isMovie'] = isMovie;
      }

      final response = await RestHelper.post(
        '/showcatwise/$page',
        body: requestBody,
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final categoryWiseResponse = CategoryWiseResponse.fromJson(
          responseData,
        );

        // Cache movies locally
        if (categoryWiseResponse.success &&
            categoryWiseResponse.result.isNotEmpty) {
          final allMovies = <MovieModel>[];
          for (final categoryData in categoryWiseResponse.result) {
            allMovies.addAll(categoryData.data);
          }
          await HiveHelper.saveMovies(allMovies);
        }

        return categoryWiseResponse;
      } else {
        throw Exception(
          'Failed to fetch category wise movies: ${response.statusCode}',
        );
      }
    } catch (e) {
      _logger.e('Error fetching category wise movies: $e');
      rethrow;
    }
  }

  @override
  Future<ShowPostsResponse> getShowPosts({
    int page = 1,
    String? languages,
    String? genres,
    String? categories,
    String? isMovie,
  }) async {
    try {
      // Prepare request body with filters
      final Map<String, dynamic> requestBody = {};

      if (languages != null && languages.isNotEmpty) {
        requestBody['lang'] = languages;
      }
      if (genres != null && genres.isNotEmpty) {
        requestBody['genre'] = genres;
      }
      if (categories != null && categories.isNotEmpty) {
        requestBody['cat'] = categories;
      }
      if (isMovie != null && isMovie.isNotEmpty) {
        requestBody['isMovie'] = isMovie;
      }

      final response = await RestHelper.post(
        '/showposts/$page',
        body: requestBody,
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final showPostsResponse = ShowPostsResponse.fromJson(responseData);

        // Cache movies locally
        if (showPostsResponse.success && showPostsResponse.result.isNotEmpty) {
          await HiveHelper.saveMovies(showPostsResponse.result);
        }

        // Also cache slider posts if available
        if (showPostsResponse.hasSliderPosts && showPostsResponse.sliderPost != null) {
          await HiveHelper.saveMovies(showPostsResponse.sliderPost!);
        }

        return showPostsResponse;
      } else {
        throw Exception('Failed to fetch show posts: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error fetching show posts: $e');
      rethrow;
    }
  }

  @override
  Future<SearchMoviesResponse> searchMovies(
    String query, {
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final response = await RestHelper.get(
        '/movies/search',
        queryParameters: {
          'q': query,
          'page': page.toString(),
          'per_page': perPage.toString(),
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final searchResponse = SearchMoviesResponse.fromJson(responseData);

        // Cache search results locally
        if (searchResponse.success && searchResponse.data.isNotEmpty) {
          await HiveHelper.saveMovies(searchResponse.data);
        }

        return searchResponse;
      } else {
        throw Exception('Failed to search movies: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error searching movies: $e');

      // Perform local search if available
      final allMovies = HiveHelper.getAllMovies();
      final filteredMovies = allMovies.where((movie) {
        return movie.title?.toLowerCase().contains(query.toLowerCase()) ==
                true ||
            movie.description?.toLowerCase().contains(query.toLowerCase()) ==
                true ||
            movie.category?.toLowerCase().contains(query.toLowerCase()) ==
                true ||
            movie.genreString.toLowerCase().contains(query.toLowerCase());
      }).toList();

      if (filteredMovies.isNotEmpty) {
        _logger.i('Returning local search results for: $query');
        return SearchMoviesResponse(
          success: true,
          message: 'Local search results',
          query: query,
          data: filteredMovies,
          totalCount: filteredMovies.length,
          page: page,
          perPage: perPage,
          hasMore: false,
        );
      }

      rethrow;
    }
  }

  @override
  Future<ShowPostsResponse> searchPosts(
    String searchQuery, {
    int page = 1,
    String? isMovie,
    String? languages,
    String? genres,
    String? categories,
  }) async {
    try {
      // Prepare request body with search parameters
      final Map<String, dynamic> requestBody = {
        'key': searchQuery, // API expects 'key' not 'search'
      };

      if (isMovie != null && isMovie.isNotEmpty) {
        requestBody['isMovie'] = isMovie;
      }
      if (languages != null && languages.isNotEmpty) {
        requestBody['lang'] = languages;
      }
      if (genres != null && genres.isNotEmpty) {
        requestBody['genre'] = genres;
      }
      if (categories != null && categories.isNotEmpty) {
        requestBody['cat'] = categories;
      }

      final response = await RestHelper.post(
        '/searchpost/$page',
        body: requestBody,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        final searchResponse = ShowPostsResponse.fromJson(responseData);

        // Cache search results locally
        if (searchResponse.success && searchResponse.result.isNotEmpty) {
          await HiveHelper.saveMovies(searchResponse.result);
        }

        return searchResponse;
      } else {
        throw Exception('Failed to search posts: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error searching posts: $e');
      rethrow;
    }
  }

  @override
  Future<MovieModel?> getMovieById(String id) async {
    try {
      final response = await RestHelper.get('/movies/$id');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final movieData = responseData['data'];
        if (movieData != null) {
          final movie = MovieModel.fromJson(movieData);

          // Cache movie locally
          await HiveHelper.saveMovie(movie);

          return movie;
        }
      }

      throw Exception('Failed to fetch movie: ${response.statusCode}');
    } catch (e) {
      _logger.e('Error fetching movie by ID: $e');

      // Return cached movie if available
      final cachedMovie = HiveHelper.getMovie(id);
      if (cachedMovie != null) {
        _logger.i('Returning cached movie: $id');
        return cachedMovie;
      }

      rethrow;
    }
  }

  @override
  Future<List<MovieModel>> getRelatedMovies(
    String movieId,
    String category,
  ) async {
    try {
      final response = await RestHelper.get(
        '/movies/$movieId/related',
        queryParameters: {'category': category},
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final List<dynamic> moviesData = responseData['data'] ?? [];
        final relatedMovies = moviesData
            .map((json) => MovieModel.fromJson(json))
            .toList();

        // Cache related movies locally
        if (relatedMovies.isNotEmpty) {
          await HiveHelper.saveMovies(relatedMovies);
        }

        return relatedMovies;
      } else {
        throw Exception(
          'Failed to fetch related movies: ${response.statusCode}',
        );
      }
    } catch (e) {
      _logger.e('Error fetching related movies: $e');

      final cachedMovies = HiveHelper.getMoviesByCategory(
        category,
      ).where((movie) => movie.id != movieId).take(10).toList();

      if (cachedMovies.isNotEmpty) {
        _logger.i('Returning cached related movies for category: $category');
        return cachedMovies;
      }

      return [];
    }
  }

  @override
  Future<FilterResponse> getFilters() async {
    try {
      final response = await RestHelper.post('/showfilter');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);

        // Handle potential null values in the response
        final Map<String, dynamic> safeResponseData = {
          'status': responseData['status'] ?? 0,
          'message': responseData['message'] ?? 'No message',
          'category': responseData['category'] ?? <String>[],
          'language': responseData['language'] ?? <String>[],
          'genre': responseData['genre'] ?? <String>[],
        };

        return FilterResponse.fromJson(safeResponseData);
      } else {
        throw Exception('Failed to fetch filters: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error fetching filters: $e');
      // Return static filters if API fails
      return FilterResponse(
        status: 1,
        message: 'Static filters',
        categoryNames: StaticFilterService.getStaticFilters().categoryNames,
        languageNames: ['English', 'हिंदी', 'ગુજરાતી', 'தமிழ்', 'മലയાളം'],
        genreNames: ['Bollywood', 'Hollywood', 'Tollywood', 'Mollywood'],
      );
    }
  }

  // Removed getFeaturedMovies - only using getCategoryWiseMovies now
}
