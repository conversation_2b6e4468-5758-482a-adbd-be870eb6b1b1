import 'package:http/http.dart' as http;
import 'package:my_video/app_imports.dart';

class AuthenticationRepositoryImpl implements AuthenticationRepository {
  @override
  Future<Map<String, dynamic>> getUnregisteredUserToken() async {
    try {
      final response = await RestHelper.post(
        AppConfig.instance.unregisteredTokenEndpoint,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Check backend status field
        if (data['status'] == 1) {
          return {
            'success': true,
            'data': {'token': data['token']},
            'message': data['message'] ?? 'Token retrieved successfully',
          };
        } else {
          return {
            'success': false,
            'message': data['message'] ?? 'Failed to get token',
          };
        }
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Failed to get token',
        };
      }
    } catch (e) {
      AppHelper.logError('Get unregistered token API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  @override
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      // First get unregistered user token if needed
      await _getUnregisteredToken();

      // Get the stored token
      final unregisteredToken = AppSharedPreference.getString('static_token');
      if (unregisteredToken == null) {
        return {
          'success': false,
          'message': 'Failed to get authentication token',
        };
      }

      final response = await RestHelper.post(
        'login',
        headers: {'auth': unregisteredToken},
        body: {'email': email},
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);

        // Check backend status field
        if (data['status'] == 1) {
          return {
            'success': true,
            'data': {'token': data['token'], 'user': data['userData']},
            'message': data['message'] ?? 'Login successful',
          };
        } else {
          return {
            'success': false,
            'message': data['message'] ?? 'Login failed',
          };
        }
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Login failed',
        };
      }
    } catch (e) {
      AppHelper.logError('Login API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  // Helper method to get unregistered user token
  Future<void> _getUnregisteredToken() async {
    try {
      final response = await RestHelper.post('unregisteredusertoken');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 1) {
          // Store static token if needed for unregistered users
          await AppSharedPreference.setString('static_token', data['token']);
        }
      }
    } catch (e) {
      AppHelper.logError('Get unregistered token error', e);
    }
  }

  @override
  Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
    String? lastName,
    String? gender,
    File? profileImage,
  }) async {
    try {
      // First get unregistered user token if needed
      await _getUnregisteredToken();

      // Get the stored token
      final unregisteredToken = AppSharedPreference.getString('static_token');
      if (unregisteredToken == null) {
        return {
          'success': false,
          'message': 'Failed to get authentication token',
        };
      }

      // Backend expects fname, lname, email, gender, profile (file)
      final Map<String, dynamic> body = {
        'fname': name,
        'lname': lastName ?? '',
        'email': email,
        'gender': gender ?? 'm', // Default to male if not provided
      };

      // For now, we'll handle profile image upload separately
      // TODO: Implement multipart upload when RestHelper supports it
      final response = await RestHelper.post(
        'login',
        headers: {'auth': unregisteredToken},
        body: body,
      );
      return _handleRegisterResponse(response);
    } catch (e) {
      AppHelper.logError('Register API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  @override
  Future<Map<String, dynamic>> googleSignIn({
    required String email,
    required String firstName,
    required String lastName,
    String? photoUrl,
    String? googleId,
  }) async {
    try {
      // First get unregistered user token if needed
      await _getUnregisteredToken();

      // Get the stored token
      final unregisteredToken = AppSharedPreference.getString('static_token');
      if (unregisteredToken == null) {
        return {
          'success': false,
          'message': 'Failed to get authentication token',
        };
      }

      // Backend expects fname, lname, email, gender for the login endpoint
      final Map<String, dynamic> body = {
        'fname': firstName,
        'lname': lastName,
        'email': email,
        'gender': 'm', // Default gender
        'googleId': googleId,
        'photoUrl': photoUrl,
      };

      final response = await RestHelper.post(
        'login',
        headers: {'auth': unregisteredToken},
        body: body,
      );

      return _handleRegisterResponse(response);
    } catch (e) {
      AppHelper.logError('Google Sign-In API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  Map<String, dynamic> _handleRegisterResponse(http.Response response) {
    if (response.statusCode == 201) {
      final data = jsonDecode(response.body);

      // Check backend status field
      if (data['status'] == 1) {
        return {
          'success': true,
          'data': {'token': data['token'], 'user': data['userData']},
          'message': data['message'] ?? 'Registration successful',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Registration failed',
        };
      }
    } else {
      final errorData = jsonDecode(response.body);
      return {
        'success': false,
        'message': errorData['message'] ?? 'Registration failed',
      };
    }
  }

  @override
  Future<Map<String, dynamic>> forgotPassword({required String email}) async {
    try {
      // Backend doesn't have forgot password endpoint
      // For now, return a mock success response
      // TODO: Implement when backend adds forgot password functionality
      await Future.delayed(
        const Duration(seconds: 2),
      ); // Simulate network delay

      return {
        'success': true,
        'message': 'Password reset instructions have been sent to your email',
      };
    } catch (e) {
      AppHelper.logError('Forgot password API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  @override
  Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String password,
  }) async {
    try {
      final response = await RestHelper.post(
        'auth/reset-password',
        body: {'token': token, 'password': password},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'data': data,
          'message': 'Password reset successful',
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Password reset failed',
        };
      }
    } catch (e) {
      AppHelper.logError('Reset password API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  @override
  Future<Map<String, dynamic>> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    try {
      final token = AppSharedPreference.getUserToken();
      final response = await RestHelper.post(
        'auth/change-password',
        headers: {'Authorization': 'Bearer $token'},
        body: {'old_password': oldPassword, 'new_password': newPassword},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'data': data,
          'message': 'Password changed successfully',
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Password change failed',
        };
      }
    } catch (e) {
      AppHelper.logError('Change password API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  @override
  Future<Map<String, dynamic>> refreshToken() async {
    try {
      final token = AppSharedPreference.getUserToken();
      final response = await RestHelper.post(
        'auth/refresh-token',
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'data': data,
          'message': 'Token refreshed successfully',
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Token refresh failed',
        };
      }
    } catch (e) {
      AppHelper.logError('Refresh token API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  @override
  Future<Map<String, dynamic>> logout() async {
    try {
      final token = AppSharedPreference.getUserToken();
      final response = await RestHelper.post(
        'logoutuser',
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data, 'message': 'Logout successful'};
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Logout failed',
        };
      }
    } catch (e) {
      AppHelper.logError('Logout API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  @override
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final token = AppSharedPreference.getUserToken();
      final response = await RestHelper.get(
        'auth/profile',
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'data': data,
          'message': 'Profile fetched successfully',
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Failed to fetch profile',
        };
      }
    } catch (e) {
      AppHelper.logError('Get profile API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }

  @override
  Future<Map<String, dynamic>> updateProfile({
    required Map<String, dynamic> userData,
  }) async {
    try {
      final token = AppSharedPreference.getUserToken();
      final response = await RestHelper.put(
        'auth/profile',
        headers: {'Authorization': 'Bearer $token'},
        body: userData,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'data': data,
          'message': 'Profile updated successfully',
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Profile update failed',
        };
      }
    } catch (e) {
      AppHelper.logError('Update profile API error', e);
      return {'success': false, 'message': 'Network error occurred'};
    }
  }
}
