// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAfjR0OU-mTGRZzq704IkbdKyAoA59ok10',
    appId: '1:526328586501:web:214d7f26c0cc535ab1576c',
    messagingSenderId: '526328586501',
    projectId: 'test-5b073',
    authDomain: 'test-5b073.firebaseapp.com',
    storageBucket: 'test-5b073.firebasestorage.app',
    measurementId: 'G-C1YR5GSRL3',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBMVtCcwp5NFlDIZZQwITm2-j06b2GkBwg',
    appId: '1:526328586501:android:f868ebeb26db1526b1576c',
    messagingSenderId: '526328586501',
    projectId: 'test-5b073',
    storageBucket: 'test-5b073.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCZXXQFpQXKPo4VMXhbrWQjBv8D5a4UT2U',
    appId: '1:526328586501:ios:e266883eb991fa00b1576c',
    messagingSenderId: '526328586501',
    projectId: 'test-5b073',
    storageBucket: 'test-5b073.firebasestorage.app',
    iosBundleId: 'com.example.myVideo',
  );
}
