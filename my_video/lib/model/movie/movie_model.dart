import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'episode_model.dart';

part 'movie_model.g.dart';

enum VideoSourceType { youtube, terabox, other }

@JsonSerializable()
@HiveType(typeId: 0)
class MovieModel extends HiveObject {
  @HiveField(0)
  @Json<PERSON><PERSON>(name: 'post_id')
  final int postId;

  @HiveField(1)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final int? sourceId;

  @HiveField(2)
  @<PERSON>son<PERSON>ey(name: 'user_id')
  final int userId;

  @HiveField(3)
  @<PERSON>son<PERSON><PERSON>(name: 'user_name')
  final String? sourceName;

  @HiveField(4)
  @<PERSON>son<PERSON>ey(name: 'isverified')
  final String? isVerified;

  @HiveField(5)
  @Json<PERSON>ey(name: 'link')
  final String? link;

  @HiveField(6)
  @<PERSON>son<PERSON><PERSON>(name: 'user_profile')
  final String? profile;

  @HiveField(7)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'title')
  final String? title;

  @HiveField(8)
  @Json<PERSON><PERSON>(name: 'ismovie')
  final int isMovie;

  @HiveField(9)
  @<PERSON>son<PERSON><PERSON>(name: 'free_ep')
  final int? freeEpisodes;

  @HiveField(10)
  @JsonKey(name: 'season')
  final int? season;

  @HiveField(11)
  @JsonKey(name: 'caption')
  final String? caption;

  @HiveField(12)
  @JsonKey(name: 'isrestric')
  final String? isRestricted;

  @HiveField(13)
  @JsonKey(name: 'views')
  final int views;

  @HiveField(14)
  @JsonKey(name: 'thumbnail')
  final String? thumbnail;

  @HiveField(15)
  @JsonKey(name: 'trailer')
  final String? trailerLink;

  @HiveField(16)
  @JsonKey(name: 'likes')
  final String? likes;

  @HiveField(17)
  @JsonKey(name: 'report_count')
  final int? reportCount;

  @HiveField(18)
  @JsonKey(name: 'imdb_rating')
  final double? imdbRating;

  @HiveField(19)
  @JsonKey(name: 'google_rating')
  final double? googleRating;

  @HiveField(20)
  @JsonKey(name: 'created_date')
  final String? createdDate;

  @HiveField(21)
  @JsonKey(name: 'genre')
  final String? genre;

  @HiveField(22)
  @JsonKey(name: 'language')
  final String? language;

  @HiveField(23)
  @JsonKey(name: 'category')
  final String? category;

  @HiveField(24)
  @JsonKey(name: 'episodes')
  final List<EpisodeModel>? episodes;

  MovieModel({
    required this.postId,
    this.sourceId,
    required this.userId,
    this.sourceName,
    this.isVerified,
    this.link,
    this.profile,
    this.title,
    required this.isMovie,
    this.freeEpisodes,
    this.season,
    this.caption,
    this.isRestricted,
    required this.views,
    this.thumbnail,
    this.trailerLink,
    this.likes,
    this.reportCount,
    this.imdbRating,
    this.googleRating,
    this.createdDate,
    this.genre,
    this.language,
    this.category,
    this.episodes,
  });

  factory MovieModel.fromJson(Map<String, dynamic> json) {
    return MovieModel(
      postId: (json['post_id'] as num).toInt(),
      sourceId: json['user_id'] as int?, // Use user_id as sourceId
      userId: (json['user_id'] as num).toInt(),
      sourceName: json['user_name'] as String?,
      isVerified: json['isverified'] as String?,
      link: json['link'] as String?,
      profile: json['user_profile'] as String?,
      title: json['title'] as String?,
      isMovie: (json['ismovie'] as num).toInt(),
      freeEpisodes: (json['free_ep'] as num?)?.toInt(),
      season: (json['season'] as num?)?.toInt(),
      caption: json['caption'] as String?,
      isRestricted: json['isrestric'] as String?,
      views: (json['views'] as num).toInt(),
      thumbnail: json['thumbnail'] as String?,
      trailerLink: json['trailer'] as String?,
      likes: json['likes'] as String?,
      reportCount: (json['report_count'] as num?)?.toInt(),
      imdbRating: (json['imdb_rating'] as num?)?.toDouble(),
      googleRating: (json['google_rating'] as num?)?.toDouble(),
      createdDate: json['created_date'] as String?,
      genre: json['genre'] as String?,
      language: json['language'] as String?,
      category: json['category'] as String?,
      episodes: (json['episodes'] as List<dynamic>?)
          ?.map((e) => EpisodeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => _$MovieModelToJson(this);

  // Helper method to extract YouTube video ID from URL
  String? get youtubeVideoId {
    if (link == null || !isYouTubeUrl) return null;
    final RegExp regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    final match = regExp.firstMatch(link!);
    return match?.group(1);
  }

  // Helper method to check if URL is YouTube
  bool get isYouTubeUrl {
    if (link == null) return false;
    return link!.contains('youtube.com') || link!.contains('youtu.be');
  }

  // Helper method to check if URL is Terabox
  bool get isTeraboxUrl {
    if (link == null) return false;
    return link!.contains('terabox.com') || link!.contains('1024terabox.com');
  }

  // Helper method to get video source type
  VideoSourceType get videoSourceType {
    if (isYouTubeUrl) return VideoSourceType.youtube;
    if (isTeraboxUrl) return VideoSourceType.terabox;
    return VideoSourceType.other;
  }

  // Helper method to get formatted duration (based on movie type)
  String get formattedDuration {
    if (isMovie == 1) return 'Movie';
    if (season != null) return 'Season $season';
    return 'Unknown';
  }

  // Helper method to get formatted rating
  String get formattedRating {
    if (imdbRating != null) return imdbRating!.toStringAsFixed(1);
    if (googleRating != null) return '${googleRating!}%';
    return 'N/A';
  }

  // Helper method to get genre string
  String get genreString {
    return genre?.isNotEmpty == true ? genre! : 'Unknown';
  }

  // Helper getters for backward compatibility
  String get id => postId.toString();
  String? get description => caption?.isNotEmpty == true ? caption : null;
  String get youtubeUrl => link ?? '';
  String get thumbnailUrl => thumbnail ?? '';
  String? get duration => formattedDuration;
  double? get rating => imdbRating;
  int? get releaseYear => null; // Not available in new API
  bool get isFeatured => false; // Can be determined by category or other logic
  int? get viewCount => views;
  DateTime? get createdAt =>
      createdDate != null ? DateTime.tryParse(createdDate!) : null;
  DateTime? get updatedAt => null;

  @override
  String toString() {
    return 'MovieModel(postId: $postId, title: $title, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MovieModel && other.postId == postId;
  }

  @override
  int get hashCode => postId.hashCode;
}
