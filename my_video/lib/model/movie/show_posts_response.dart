import 'package:json_annotation/json_annotation.dart';
import 'package:my_video/app_imports.dart';

part 'show_posts_response.g.dart';

@JsonSerializable()
class ShowPostsResponse {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'info')
  final String? info;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'status')
  final int status;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'message')
  final String message;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'sliderPost')
  final List<MovieModel>? sliderPost;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'result')
  final List<MovieModel> result;

  @Json<PERSON>ey(name: 'query')
  final String? query;

  ShowPostsResponse({
    this.info,
    required this.status,
    required this.message,
    this.sliderPost,
    required this.result,
    this.query,
  });

  factory ShowPostsResponse.fromJson(Map<String, dynamic> json) =>
      _$ShowPostsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ShowPostsResponseToJson(this);

  bool get success => status == 1;
  bool get hasSliderPosts => sliderPost?.isNotEmpty ?? false;
  bool get hasResults => result.isNotEmpty;
  int get totalResults => result.length;
}
