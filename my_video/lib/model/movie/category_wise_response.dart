import 'package:json_annotation/json_annotation.dart';
import 'movie_model.dart';

part 'category_wise_response.g.dart';

@JsonSerializable()
class CategoryWiseResponse {
  @JsonKey(name: 'status')
  final int status;

  @Json<PERSON>ey(name: 'message')
  final String message;

  @JsonKey(name: 'result')
  final List<CategoryWiseData> result;

  @Json<PERSON>ey(name: 'posts')
  final List<MovieModel>? posts;

  CategoryWiseResponse({
    required this.status,
    required this.message,
    required this.result,
    this.posts,
  });

  factory CategoryWiseResponse.fromJson(Map<String, dynamic> json) =>
      _$CategoryWiseResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryWiseResponseToJson(this);

  bool get success => status == 1;
}

@JsonSerializable()
class CategoryWiseData {
  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'data')
  final List<MovieModel> data;

  CategoryWiseData({required this.name, required this.data});

  factory CategoryWiseData.fromJson(Map<String, dynamic> json) =>
      _$CategoryWiseDataFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryWiseDataToJson(this);

  @override
  String toString() {
    return 'CategoryWiseData(name: $name, dataCount: ${data.length})';
  }
}
