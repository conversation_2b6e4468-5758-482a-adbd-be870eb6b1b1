// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'movie_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MovieModelAdapter extends TypeAdapter<MovieModel> {
  @override
  final int typeId = 0;

  @override
  MovieModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MovieModel(
      postId: fields[0] as int,
      sourceId: fields[1] as int?,
      userId: fields[2] as int,
      sourceName: fields[3] as String?,
      isVerified: fields[4] as String?,
      link: fields[5] as String?,
      profile: fields[6] as String?,
      title: fields[7] as String?,
      isMovie: fields[8] as int,
      freeEpisodes: fields[9] as int?,
      season: fields[10] as int?,
      caption: fields[11] as String?,
      isRestricted: fields[12] as String?,
      views: fields[13] as int,
      thumbnail: fields[14] as String?,
      trailerLink: fields[15] as String?,
      likes: fields[16] as String?,
      reportCount: fields[17] as int?,
      imdbRating: fields[18] as double?,
      googleRating: fields[19] as double?,
      createdDate: fields[20] as String?,
      genre: fields[21] as String?,
      language: fields[22] as String?,
      category: fields[23] as String?,
      episodes: (fields[24] as List?)?.cast<EpisodeModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, MovieModel obj) {
    writer
      ..writeByte(25)
      ..writeByte(0)
      ..write(obj.postId)
      ..writeByte(1)
      ..write(obj.sourceId)
      ..writeByte(2)
      ..write(obj.userId)
      ..writeByte(3)
      ..write(obj.sourceName)
      ..writeByte(4)
      ..write(obj.isVerified)
      ..writeByte(5)
      ..write(obj.link)
      ..writeByte(6)
      ..write(obj.profile)
      ..writeByte(7)
      ..write(obj.title)
      ..writeByte(8)
      ..write(obj.isMovie)
      ..writeByte(9)
      ..write(obj.freeEpisodes)
      ..writeByte(10)
      ..write(obj.season)
      ..writeByte(11)
      ..write(obj.caption)
      ..writeByte(12)
      ..write(obj.isRestricted)
      ..writeByte(13)
      ..write(obj.views)
      ..writeByte(14)
      ..write(obj.thumbnail)
      ..writeByte(15)
      ..write(obj.trailerLink)
      ..writeByte(16)
      ..write(obj.likes)
      ..writeByte(17)
      ..write(obj.reportCount)
      ..writeByte(18)
      ..write(obj.imdbRating)
      ..writeByte(19)
      ..write(obj.googleRating)
      ..writeByte(20)
      ..write(obj.createdDate)
      ..writeByte(21)
      ..write(obj.genre)
      ..writeByte(22)
      ..write(obj.language)
      ..writeByte(23)
      ..write(obj.category)
      ..writeByte(24)
      ..write(obj.episodes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MovieModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MovieModel _$MovieModelFromJson(Map<String, dynamic> json) => MovieModel(
      postId: (json['post_id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      sourceName: json['user_name'] as String?,
      isVerified: json['isverified'] as String?,
      link: json['link'] as String?,
      profile: json['user_profile'] as String?,
      title: json['title'] as String?,
      isMovie: (json['ismovie'] as num).toInt(),
      freeEpisodes: (json['free_ep'] as num?)?.toInt(),
      season: (json['season'] as num?)?.toInt(),
      caption: json['caption'] as String?,
      isRestricted: json['isrestric'] as String?,
      views: (json['views'] as num).toInt(),
      thumbnail: json['thumbnail'] as String?,
      trailerLink: json['trailer'] as String?,
      likes: json['likes'] as String?,
      reportCount: (json['report_count'] as num?)?.toInt(),
      imdbRating: (json['imdb_rating'] as num?)?.toDouble(),
      googleRating: (json['google_rating'] as num?)?.toDouble(),
      createdDate: json['created_date'] as String?,
      genre: json['genre'] as String?,
      language: json['language'] as String?,
      category: json['category'] as String?,
      episodes: (json['episodes'] as List<dynamic>?)
          ?.map((e) => EpisodeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MovieModelToJson(MovieModel instance) =>
    <String, dynamic>{
      'post_id': instance.postId,
      'user_id': instance.userId,
      'user_name': instance.sourceName,
      'isverified': instance.isVerified,
      'link': instance.link,
      'user_profile': instance.profile,
      'title': instance.title,
      'ismovie': instance.isMovie,
      'free_ep': instance.freeEpisodes,
      'season': instance.season,
      'caption': instance.caption,
      'isrestric': instance.isRestricted,
      'views': instance.views,
      'thumbnail': instance.thumbnail,
      'trailer': instance.trailerLink,
      'likes': instance.likes,
      'report_count': instance.reportCount,
      'imdb_rating': instance.imdbRating,
      'google_rating': instance.googleRating,
      'created_date': instance.createdDate,
      'genre': instance.genre,
      'language': instance.language,
      'category': instance.category,
      'episodes': instance.episodes,
    };
