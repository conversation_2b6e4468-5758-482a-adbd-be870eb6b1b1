// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'show_posts_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShowPostsResponse _$ShowPostsResponseFromJson(Map<String, dynamic> json) =>
    ShowPostsResponse(
      info: json['info'] as String?,
      status: (json['status'] as num).toInt(),
      message: json['message'] as String,
      sliderPost: (json['sliderPost'] as List<dynamic>?)
          ?.map((e) => MovieModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      result: (json['result'] as List<dynamic>)
          .map((e) => MovieModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      query: json['query'] as String?,
    );

Map<String, dynamic> _$ShowPostsResponseToJson(ShowPostsResponse instance) =>
    <String, dynamic>{
      'info': instance.info,
      'status': instance.status,
      'message': instance.message,
      'sliderPost': instance.sliderPost,
      'result': instance.result,
      'query': instance.query,
    };
