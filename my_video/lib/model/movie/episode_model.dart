import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'episode_model.g.dart';

@JsonSerializable()
@HiveType(typeId: 3)
class EpisodeModel extends HiveObject {
  @HiveField(0)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'id')
  final int id;

  @HiveField(1)
  @<PERSON>son<PERSON>ey(name: 'link')
  final String? link;

  @HiveField(2)
  @<PERSON>son<PERSON>ey(name: 'sub_title')
  final String? subTitle;

  @HiveField(3)
  @<PERSON>son<PERSON>ey(name: 'is_trailer')
  final bool isTrailer;

  @HiveField(4)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'title')
  final String? title;

  EpisodeModel({
    required this.id,
    this.link,
    this.subTitle,
    required this.isTrailer,
    this.title,
  });

  factory EpisodeModel.fromJson(Map<String, dynamic> json) =>
      _$EpisodeModelFromJson(json);

  Map<String, dynamic> toJson() => _$EpisodeModelToJson(this);

  @override
  String toString() {
    return 'EpisodeModel(id: $id, subTitle: ${subTitle ?? "null"}, isTrailer: $isTrailer)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EpisodeModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
