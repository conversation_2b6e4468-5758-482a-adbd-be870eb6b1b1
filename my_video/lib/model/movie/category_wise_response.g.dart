// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_wise_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryWiseResponse _$CategoryWiseResponseFromJson(
        Map<String, dynamic> json) =>
    CategoryWiseResponse(
      status: (json['status'] as num).toInt(),
      message: json['message'] as String,
      result: (json['result'] as List<dynamic>)
          .map((e) => CategoryWiseData.fromJson(e as Map<String, dynamic>))
          .toList(),
      posts: (json['posts'] as List<dynamic>?)
          ?.map((e) => MovieModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CategoryWiseResponseToJson(
        CategoryWiseResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'result': instance.result,
      'posts': instance.posts,
    };

CategoryWiseData _$CategoryWiseDataFromJson(Map<String, dynamic> json) =>
    CategoryWiseData(
      name: json['name'] as String,
      data: (json['data'] as List<dynamic>)
          .map((e) => MovieModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CategoryWiseDataToJson(CategoryWiseData instance) =>
    <String, dynamic>{
      'name': instance.name,
      'data': instance.data,
    };
