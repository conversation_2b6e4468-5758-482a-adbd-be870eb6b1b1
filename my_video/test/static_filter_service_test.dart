import 'package:flutter_test/flutter_test.dart';
import 'package:my_video/app/services/static_filter_service.dart';
import 'package:my_video/model/filter/filter_model.dart';

void main() {
  group('StaticFilterService Tests', () {
    test('should return static filters with correct data', () {
      // Act
      final filterResponse = StaticFilterService.getStaticFilters();

      // Assert
      expect(filterResponse.status, equals(1));
      expect(filterResponse.message, equals("Static filters loaded"));
      expect(filterResponse.success, isTrue);
      
      // Check languages
      expect(filterResponse.languageNames.length, equals(18));
      expect(filterResponse.languageNames.contains('English'), isTrue);
      expect(filterResponse.languageNames.contains('हिन्दी'), isTrue);
      expect(filterResponse.languageNames.contains('ગુજરાતી'), isTrue);
      
      // Check genres
      expect(filterResponse.genreNames.length, equals(18));
      expect(filterResponse.genreNames.contains('Bollywood'), isTrue);
      expect(filterResponse.genreNames.contains('Hollywood'), isTrue);
      expect(filterResponse.genreNames.contains('Tollywood'), isTrue);
      
      // Check categories
      expect(filterResponse.categoryNames.length, equals(26));
      expect(filterResponse.categoryNames.contains('Action'), isTrue);
      expect(filterResponse.categoryNames.contains('Comedy'), isTrue);
      expect(filterResponse.categoryNames.contains('Drama'), isTrue);
    });

    test('should return default preferences with English and Bollywood', () {
      // Act
      final defaultPreferences = StaticFilterService.getDefaultPreferences();

      // Assert
      expect(defaultPreferences.selectedLanguages.length, equals(1));
      expect(defaultPreferences.selectedLanguages.first.language, equals('English'));
      expect(defaultPreferences.selectedLanguages.first.langId, equals(1));
      
      expect(defaultPreferences.selectedGenres.length, equals(1));
      expect(defaultPreferences.selectedGenres.first.genre, equals('Bollywood'));
      expect(defaultPreferences.selectedGenres.first.genreId, equals(8));
      
      expect(defaultPreferences.selectedCategories.length, equals(0));
    });

    test('should validate languages correctly', () {
      // Act & Assert
      expect(StaticFilterService.isValidLanguage('English'), isTrue);
      expect(StaticFilterService.isValidLanguage('हिन्दी'), isTrue);
      expect(StaticFilterService.isValidLanguage('InvalidLanguage'), isFalse);
    });

    test('should validate genres correctly', () {
      // Act & Assert
      expect(StaticFilterService.isValidGenre('Bollywood'), isTrue);
      expect(StaticFilterService.isValidGenre('Hollywood'), isTrue);
      expect(StaticFilterService.isValidGenre('InvalidGenre'), isFalse);
    });

    test('should validate categories correctly', () {
      // Act & Assert
      expect(StaticFilterService.isValidCategory('Action'), isTrue);
      expect(StaticFilterService.isValidCategory('Comedy'), isTrue);
      expect(StaticFilterService.isValidCategory('InvalidCategory'), isFalse);
    });

    test('should get language by name', () {
      // Act
      final english = StaticFilterService.getLanguageByName('English');
      final hindi = StaticFilterService.getLanguageByName('हिन्दी');
      final invalid = StaticFilterService.getLanguageByName('InvalidLanguage');

      // Assert
      expect(english, isNotNull);
      expect(english!.language, equals('English'));
      expect(english.langId, equals(1));
      
      expect(hindi, isNotNull);
      expect(hindi!.language, equals('हिन्दी'));
      expect(hindi.langId, equals(7));
      
      expect(invalid, isNull);
    });

    test('should get genre by name', () {
      // Act
      final bollywood = StaticFilterService.getGenreByName('Bollywood');
      final hollywood = StaticFilterService.getGenreByName('Hollywood');
      final invalid = StaticFilterService.getGenreByName('InvalidGenre');

      // Assert
      expect(bollywood, isNotNull);
      expect(bollywood!.genre, equals('Bollywood'));
      expect(bollywood.genreId, equals(8));
      
      expect(hollywood, isNotNull);
      expect(hollywood!.genre, equals('Hollywood'));
      expect(hollywood.genreId, equals(1));
      
      expect(invalid, isNull);
    });

    test('should get category by name', () {
      // Act
      final action = StaticFilterService.getCategoryByName('Action');
      final comedy = StaticFilterService.getCategoryByName('Comedy');
      final invalid = StaticFilterService.getCategoryByName('InvalidCategory');

      // Assert
      expect(action, isNotNull);
      expect(action!.catName, equals('Action'));
      expect(action.catId, equals(1));
      
      expect(comedy, isNotNull);
      expect(comedy!.catName, equals('Comedy'));
      expect(comedy.catId, equals(7));
      
      expect(invalid, isNull);
    });

    test('should convert to filter objects correctly', () {
      // Act
      final filterResponse = StaticFilterService.getStaticFilters();
      final languages = filterResponse.languages;
      final genres = filterResponse.genres;
      final categories = filterResponse.categories;

      // Assert
      expect(languages.length, equals(18));
      expect(languages.first.language, equals('English'));
      expect(languages.first.langId, equals(1));
      
      expect(genres.length, equals(18));
      expect(genres.first.genre, equals('Hollywood'));
      expect(genres.first.genreId, equals(1));
      
      expect(categories.length, equals(26));
      expect(categories.first.catName, equals('Action'));
      expect(categories.first.catId, equals(1));
    });
  });
}
