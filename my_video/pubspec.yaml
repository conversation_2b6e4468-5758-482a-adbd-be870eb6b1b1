name: my_video
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  shimmer: ^3.0.0
  lottie: ^3.1.2
  cached_network_image: ^3.3.1
  flutter_svg: ^2.0.10+1
  get: ^4.6.6
  get_it: ^8.0.3
  intl: ^0.20.2
  go_router: ^16.0.0
  logger: ^2.2.0
  permission_handler: ^12.0.1
  device_info_plus: ^11.5.0
  dropdown_button2: ^2.3.9
  bot_toast: ^4.1.3
  file_picker: ^10.2.0
  image_picker: ^1.1.2
  internet_connection_checker: ^3.0.1
  url_launcher: ^6.3.0
  http: ^1.2.1
  shared_preferences: ^2.2.3
  json_annotation: ^4.9.0
#  external_path: ^2.2.0
  path_provider: ^2.1.3
  flutter_html: ^3.0.0
  google_fonts: ^6.2.1
  translator: ^1.0.3+1
  html: ^0.15.6

  # Movie Streaming App Dependencies
  youtube_player_iframe: ^5.2.1
  google_mobile_ads: ^6.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_inappwebview: ^6.0.0
  share_plus: ^11.0.0
  firebase_core: ^4.0.0
  google_sign_in: ^6.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^6.0.0
  build_runner: ^2.4.11
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/images/
    - assets/animation/
    - assets/data/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
