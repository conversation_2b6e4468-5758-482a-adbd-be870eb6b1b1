{"info": "Data is getting from cache", "status": 1, "message": "Found data of user post", "sliderPost": [], "result": [{"post_id": 15744, "user_id": 20, "user_name": "<PERSON><PERSON> verma", "link": "https://1024terabox.com/s/1TeFLPX8o94iWHWmTh94MZw", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1750913861794-abaf5ccf95eb9db2ca72.webp", "title": "Si<PERSON><PERSON>", "ismovie": 1, "season": null, "caption": "A young, passionate individual challenges a widespread corrupt system, advocates for the rights of ordinary citizens and disrupts established power structures in a country marked by unfairness.", "isrestric": "false", "views": 60480834, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1743398746232-53d2ee4285f042827bec.webp", "trailer": "https://www.youtube.com/watch?v=BAk5ZCoTWY8", "likes": "", "report_count": 9, "imdb_rating": 5.1, "google_rating": 78, "created_date": "2025-03-31T05:25:06.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action,Drama", "episodes": []}, {"post_id": 15688, "user_id": 3976, "user_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://www.youtube.com/watch?v=RirUtcYGYLc", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1747029409084-b9340eb4eff2412d099f.webp", "title": "<PERSON><PERSON><PERSON><PERSON>", "ismovie": 1, "season": 0, "caption": "Two childhood friends, <PERSON><PERSON><PERSON> and <PERSON>, turn foes when one of them becomes a policeman while the other gets involved in criminal activities.", "isrestric": "false", "views": 693831, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1743074082233-ccedbccc66dbaf0dc068.webp", "trailer": "https://www.youtube.com/watch?v=2Fahk5o80Eg", "likes": "", "report_count": null, "imdb_rating": 5.5, "google_rating": 74, "created_date": "2025-03-27T08:40:02.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action", "episodes": []}, {"post_id": 15665, "user_id": 3976, "user_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://www.youtube.com/watch?v=wO8XKroyAaQ", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1747029409084-b9340eb4eff2412d099f.webp", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": 0, "caption": "Since childhood, <PERSON><PERSON><PERSON> has held a deep grudge against his father for deserting him as a boy. Will his attraction to an unlikely love interest cure him of his resentment, or will it make things worse?", "isrestric": "true", "views": 62684, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1749451323012-9edc11ea6defa2b24875.webp", "trailer": "https://www.youtube.com/watch?v=iSm6sELY8W4", "likes": "", "report_count": null, "imdb_rating": 5.8, "google_rating": 69, "created_date": "2025-03-27T07:55:34.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action", "episodes": []}, {"post_id": 15664, "user_id": 3976, "user_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://www.youtube.com/watch?v=DwYi40geuWs", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1747029409084-b9340eb4eff2412d099f.webp", "title": "Zameen", "ismovie": 1, "season": 0, "caption": "When a few terrorists hijack an Indian aeroplane and demand for their leader's release, <PERSON><PERSON> and Colonel <PERSON><PERSON><PERSON> team up to punish the perpetrators and thwart their evil plans.", "isrestric": "false", "views": 11870744, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1743073683326-3de99e7880cffffd67e4.webp", "trailer": "https://www.youtube.com/watch?v=M4K7R7-Yd9I", "likes": "", "report_count": null, "imdb_rating": 5.4, "google_rating": 65, "created_date": "2025-03-27T07:53:10.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action", "episodes": []}, {"post_id": 15661, "user_id": 3976, "user_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://www.youtube.com/watch?v=a7AUdf5lkvs", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1747029409084-b9340eb4eff2412d099f.webp", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": 0, "caption": "<PERSON><PERSON>, a short-tempered man, is a protector of justice and is greatly feared. He saves the CM in an assassination attempt and wants to punish the culprits responsible for the death of his siblings.", "isrestric": "false", "views": 26578796, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1743074876072-0815be7161b1ad30a3dc.webp", "trailer": "https://www.youtube.com/watch?v=VNiICPVt8oM", "likes": "", "report_count": null, "imdb_rating": 5.9, "google_rating": 62, "created_date": "2025-03-27T07:43:47.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action,Drama", "episodes": []}, {"post_id": 15659, "user_id": 3976, "user_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://www.youtube.com/watch?v=ELZs0mGhGvc", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1747029409084-b9340eb4eff2412d099f.webp", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": 0, "caption": "A boy, born into a family of varying religions, finds himself embroiled in communal riots. Soon, his mother's death becomes an issue raised to fulfil political agendas.", "isrestric": "false", "views": 3257083, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1743059856200-bceb8443ee416bdc00a9.webp", "trailer": "https://www.youtube.com/watch?v=no5CQfjtnL8", "likes": "", "report_count": null, "imdb_rating": 7.9, "google_rating": 76, "created_date": "2025-03-27T06:52:28.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action,Drama", "episodes": []}, {"post_id": 15658, "user_id": 3976, "user_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://www.youtube.com/watch?v=GIzWcYStgRw", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1747029409084-b9340eb4eff2412d099f.webp", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": 0, "caption": "A law student who is short-tempered has committed murder in his childhood. Years later, when his sister is raped and killed, he goes on a murderous spree once again.", "isrestric": "false", "views": 64032, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1743059485973-4c8d3c302524663da6af.webp", "trailer": "https://www.youtube.com/watch?v=YeuyJvPl76E", "likes": "", "report_count": null, "imdb_rating": 4.5, "google_rating": 55, "created_date": "2025-03-27T06:50:11.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action,Drama", "episodes": []}, {"post_id": 15657, "user_id": 3976, "user_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://www.youtube.com/watch?v=tOLMKBLxjrA", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1747029409084-b9340eb4eff2412d099f.webp", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": 0, "caption": "<PERSON> is hired as a bodyguard for <PERSON><PERSON>, and the two grow closer to each other over time. However, things get complicated when he comes across <PERSON><PERSON>, the man who molested his sister.", "isrestric": "false", "views": 18223315, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1743060022007-8bc5d8badae8c20194ac.webp", "trailer": "https://www.youtube.com/watch?v=Qgfs1xc31LE", "likes": "", "report_count": null, "imdb_rating": 4.2, "google_rating": 63, "created_date": "2025-03-27T06:45:13.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action,Drama", "episodes": []}, {"post_id": 15048, "user_id": 400140, "user_name": "<PERSON><PERSON>", "link": "https://www.youtube.com/watch?v=ulVo0UnBQ6A", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1739960818421-9aa171b1f14f76c53d5b.webp", "title": "Horror Story", "ismovie": 1, "season": 0, "caption": "दोस्तों का एक समूह एक दूसरे दोस्त को विदाई देने के लिए मिलता है, जो विदेश जा रहा है। वे एक भूतिया होटल में रात बिताने का फैसला करते हैं, जो उनकी ज़िंदगी को एक ऐसे तरीके से बदल देता है जिसकी उन्होंने कम से कम उम्मीद की थी।", "isrestric": "false", "views": 9244315, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1739963181347-803d7a2dcbca838676ae.webp", "trailer": "https://www.youtube.com/watch?v=pdljX50YAZ0", "likes": "", "report_count": 2, "imdb_rating": 4.2, "google_rating": 80, "created_date": "2025-02-19T10:29:45.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Drama", "episodes": []}, {"post_id": 13767, "user_id": 20, "user_name": "<PERSON><PERSON> verma", "link": "https://www.youtube.com/watch?v=GZkjGnh0RMw", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1750913861794-abaf5ccf95eb9db2ca72.webp", "title": "<PERSON><PERSON>", "ismovie": 1, "season": null, "caption": "\"A Film by <PERSON><PERSON><PERSON> Pictures. In Association with Anil <PERSON> Film & Communication Network.  Produced by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>\"", "isrestric": "false", "views": 27565, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1734605889797-d55c8696a02a99f70faa.webp", "trailer": "https://www.youtube.com/watch?v=uxLR6529mdw", "likes": "", "report_count": 1, "imdb_rating": 6.1, "google_rating": 72, "created_date": "2024-12-19T10:58:09.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Drama", "episodes": []}, {"post_id": 13765, "user_id": 20, "user_name": "<PERSON><PERSON> verma", "link": "https://www.youtube.com/watch?v=3ZNhzL6_WtE", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1750913861794-abaf5ccf95eb9db2ca72.webp", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": null, "caption": "\"<PERSON><PERSON><PERSON>\" is a heartfelt romantic drama directed by <PERSON><PERSON><PERSON>. It tells the tale of love, betrayal, and second chances as <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s lives take an unexpected turn on their wedding day. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> deliver powerful performances in this emotional rollercoaster. With its engaging storyline, memorable moments, and soulful music, \"<PERSON><PERSON><PERSON>\" is a must-watch for all romantics at heart.", "isrestric": "true", "views": 46358, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1736751407576-9d67e984d5e4bc5a82c4.webp", "trailer": "https://www.youtube.com/watch?v=An4vqppEWXU", "likes": "", "report_count": null, "imdb_rating": 4.4, "google_rating": 53, "created_date": "2024-12-19T10:54:46.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Drama", "episodes": []}, {"post_id": 13763, "user_id": 19, "user_name": "j<PERSON><PERSON> malkar", "link": "https://www.youtube.com/watch?v=Ff1XybhrMlw", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1667545681945-12-wallpaperflare.com_wallpaper (4).jpg", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": null, "caption": "<PERSON><PERSON><PERSON> presents in association with Karma Features Pvt. Ltd official movie trailer of the upcoming Bollywood movie in 2017  \"Simran\". The film is directed by   <PERSON><PERSON>, produced by  <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> & <PERSON><PERSON>, Featuring <PERSON><PERSON> in the lead role.", "isrestric": "false", "views": 29564, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1734605405065-93e30aae2f9cfb3a8b5a.webp", "trailer": "https://www.youtube.com/watch?v=_LUe4r6eeQA", "likes": "", "report_count": null, "imdb_rating": 4.1, "google_rating": 65, "created_date": "2024-12-19T10:50:05.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Drama", "episodes": []}, {"post_id": 13762, "user_id": 19, "user_name": "j<PERSON><PERSON> malkar", "link": "https://www.youtube.com/watch?v=a2Fta6ejT78", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1667545681945-12-wallpaperflare.com_wallpaper (4).jpg", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": null, "caption": "Presenting the Official Trailer of <PERSON><PERSON><PERSON> starring <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>.", "isrestric": "false", "views": 321922, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1747373495906-62dee9353cefcb10d485.webp", "trailer": "https://www.youtube.com/watch?v=5Paw8kazId8", "likes": "", "report_count": 2, "imdb_rating": 6.1, "google_rating": 75, "created_date": "2024-12-19T10:49:12.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action,Drama", "episodes": []}, {"post_id": 13761, "user_id": 19, "user_name": "j<PERSON><PERSON> malkar", "link": "https://www.youtube.com/watch?v=Iy-6jmQCcrI", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1667545681945-12-wallpaperflare.com_wallpaper (4).jpg", "title": "<PERSON><PERSON><PERSON>", "ismovie": 1, "season": null, "caption": "<PERSON><PERSON><PERSON> and <PERSON><PERSON> present the official Trailer of the upcoming Indian film \"Raab<PERSON>\" a film by <PERSON><PERSON> and Produced by  <PERSON><PERSON>.The Film stars <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in lead roles with Music by <PERSON><PERSON><PERSON>.", "isrestric": "false", "views": 14806, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1734605202760-b33aad93f3f7b3054bc2.webp", "trailer": "https://www.youtube.com/watch?v=YXjYfpqg8Z0", "likes": "", "report_count": 2, "imdb_rating": 4.2, "google_rating": 64, "created_date": "2024-12-19T10:46:42.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Drama", "episodes": []}, {"post_id": 13285, "user_id": 261008, "user_name": "sAMEER SHAI", "link": "https://www.youtube.com/watch?v=mkyk2ym4tGM", "isverified": "true", "free_ep": null, "user_profile": "https://1672415082.rsc.cdn77.org/upload/profile/1732111220819-a64fc26edffda59e61c2.webp", "title": "Robot", "ismovie": 1, "season": 0, "caption": "A brilliant scientist creates a humanoid robot to protect mankind, but things go awry when human emotions are programmed and inner turmoil causes the robot to switch sides.", "isrestric": "false", "views": 79506, "thumbnail": "https://1672415082.rsc.cdn77.org/upload/thumbnail/1732703729149-023ebceefeffe17bc8b2.webp", "trailer": "https://www.youtube.com/watch?v=RprCwSsUe5E", "likes": "", "report_count": 3, "imdb_rating": 7.1, "google_rating": 77, "created_date": "2024-11-27T09:48:08.000Z", "genre": "Bollywood", "language": "हिन्दी", "category": "Action", "episodes": []}], "query": "\n      SELECT \n        p.id AS post_id,\n        p.source_id AS user_id,\n        p.source_name AS user_name,\n        p.link,\n        p.isverified,\n        p.free_ep,\n        COALESCE(\n          NULLIF(\n            CASE \n              WHEN u.profile LIKE '%http://%' OR u.profile LIKE '%https://%' OR u.profile LIKE '%www.%' \n              THEN u.profile \n              ELSE NULL \n            END, \n            ''\n          ),\n          CONCAT(?, NULLIF(u.profile, ''))\n        ) AS user_profile,\n        p.title,\n        p.ismovie,\n        p.season,\n        p.caption,\n        p.isrestric,\n        p.views,\n        CASE \n          WHEN p.thumbnail IS NULL THEN '' \n          ELSE CONCAT(?, p.thumbnail) \n        END AS thumbnail,\n        p.trailer_link AS trailer,\n        '' AS likes,\n        p.report_count,\n        p.imdb_rating,\n        p.google_rating,\n        p.created_date,\n        g.genre,\n        GROUP_CONCAT(DISTINCT l.language ORDER BY l.language) AS language,\n        GROUP_CONCAT(DISTINCT c.cat_name ORDER BY c.cat_name) AS category\n      FROM post_tbl p\n      INNER JOIN user_tbl u ON p.source_id = u.id\n      INNER JOIN post_language pl ON p.id = pl.post_id\n      INNER JOIN language_tbl l ON pl.lang_id = l.lang_id\n      INNER JOIN post_category pc ON p.id = pc.post_id\n      INNER JOIN category_tbl c ON pc.cat_id = c.cat_id\n      INNER JOIN genre_tbl g ON p.genre_id = g.id\n     WHERE pl.lang_id IN (?,?) AND pc.cat_id IN (?,?) AND g.id IN (?) AND p.isverified = 'true' AND p.ismovie != 2\n      GROUP BY p.id, p.source_id, p.source_name, p.isverified, p.link, p.source_profile, \n               p.title, p.ismovie, p.season, p.caption, p.isrestric, p.views, \n               p.report_count, p.imdb_rating, p.google_rating, p.created_date, g.genre\n      ORDER BY p.created_date DESC\n    limit ? offset ?"}